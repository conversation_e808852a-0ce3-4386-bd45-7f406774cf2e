# STRIPE_API_KEY=
# STRIPE_SECRET_KEY=
# STRIPE_WEBHOOK_SECRET=
# STRIPE_WEBHOOK_SECRET_TEST=
# STRIPE_CHECKOUT_SUCCESS_URL=
# STRIPE_CHECKOUT_CANCEL_URL=

# SUPABASE_URL=
# SUPABASE_KEY=
# SUPABASE_JWT_SECRET=
# DATABASE_URL=

# DB_USER=
# DB_PASSWORD=
# DB_HOST=
# DB_PORT=
# DB_NAME=

# docker frontend-validator
#FORCE_HTTPS=http_x_forwarded_proto
FORCE_HTTPS=no
API_PREFIX=/api
API_HOST=*********
API_PORT=3000

INPUT_BUILD_ARGS="FORCE_HTTPS=${FORCE_HTTPS}\nAPI_PREFIX=${API_PREFIX}\nAPI_HOST=${API_HOST}\nAPI_PORT=${API_PORT}"

# GPU_API_BASE_URL=
# GPU_PRICE_PER_RAM=
# GPU_PRICE_PER_CPU=

BALANCE_MONITOR_INTERVAL_IN_MINUTES = 1
BALANCE_BUFFER_TIME_IN_MINUTES = 60

# GOOGLE_RECAPTCHA_SITE_KEY=
# GOOGLE_RECAPTCHA_SECRET_KEY=

# GCP Pub/Sub Configuration
# PUBSUB_ENABLED=false
# GCP_PROJECT_ID=your-gcp-project-id
# PUBSUB_TOPIC_NAME=gpu-events
# PUBSUB_SUBSCRIPTION_NAME=gpu-events-subscription
# GCP_KEY_FILENAME=/path/to/service-account-key.json

GPU_LOG_LEVEL=dev

# GPU_API_CLIENT_KEY=
# GPU_API_CLIENT_CERT=
# GPU_API_CA_CERT=
# SUPABASE_SSL_CERT=

