# Locally CodeQL CLI Integration with Nx

This documentation provides a step-by-step guide to integrating **CodeQL CLI** into the `dashboard` (frontend) and `server` (backend) projects within our monorepo managed by Nx.

## Folder Structure

The integration uses the following folder structure to organize CodeQL databases and analysis reports:

```
codeql/
  databases/
    dashboard/
    server/
  reports/
    frontend-results.sarif
    backend-results.sarif
```

## Prerequisites

1. Install CodeQL CLI (CodeQL Bundle: CodeQL CLI + compatible version of the queries and libraries) and and ensure it is available in your PATH.

   - Download the Appropriate Bundle for Your System: follow this link[Download CodeQL Bundle](https://github.com/github/codeql-action/releases/latest).
   - Extract the Bundle: after downloading, extract the ZIP file to a directory of your choice.
     - Recommended folder is:
       - Windows:  
         C:\Program Files\CodeQL or C:\Users\<USER>\CodeQL (if restricted permissions)
       - Mac OS:  
         /usr/local/codeql or ~/codeql (if restricted permissions)
       - Ubuntu:  
         /opt/codeql or ~/.local/codeql (if you don’t have root access)
   - Add CodeQL to Your PATH: Add the extracted folder (which contains the codeql executable) to your system's PATH.

     - On Linux/macOS:
       ```bash
       echo 'export PATH=$PATH:/usr/local/codeql'  >>  ~/.bashrc  # For bash
       echo 'export PATH=$PATH:/usr/local/codeql'  >>  ~/.bashrc  # For zsh
       ```
     - On Windows: Add the path to the codeql binary in the System Environment Variables.

   - Reload your shell configuration:
     ```
       source ~/.bashrc   # For bash
       source ~/.zshrc    # For zsh
     ```
     Sometimes you need to reload you Terminal.
   - Test the Installation: Run
     ```
       codeql version
     ```

2. Install Nx CLI locally (if not yet installed):
   ```bash
   pnpm install nx
   ```
3. Ensure your monorepo uses Nx. Locate the `project.json` file for each project (`dashboard` and `server`), as we will update them to include CodeQL-specific targets.

---

## Configuring CodeQL Targets

### 1. Backend (`server`) Configuration

Update the `project.json` file for the `server` project to include the `codeql` target:

```json
{
  "codeql": {
    "executor": "nx:run-commands",
    "options": {
      "command": "mkdir -p ./codeql/databases/server ./codeql/reports && codeql database create ./codeql/databases/server --language=javascript --overwrite --command='pnpm exec nx build server --configuration=development' && codeql database analyze ./codeql/databases/server codeql-javascript-queries --format=sarifv2.1.0 --output=./codeql/reports/backend-results.sarif"
    }
  }
}
```

### 2. Frontend (`dashboard`) Configuration

Similarly, update the `project.json` file for the `dashboard` project:

```json
{
  "codeql": {
    "executor": "nx:run-commands",
    "options": {
      "command": "mkdir -p ./codeql/databases/dashboard ./codeql/reports && codeql database create ./codeql/databases/dashboard --language=javascript --overwrite --command='pnpm exec nx build dashboard --configuration=development' && codeql database analyze ./codeql/databases/dashboard codeql-javascript-queries --format=sarifv2.1.0 --output=./codeql/reports/frontend-results.sarif"
    }
  }
}
```

---

## Running CodeQL Analysis

### Backend Analysis

Run the following command to analyze the backend project (`server`):

```bash
pnpm exec nx run server:codeql
```

- This will:
  - Create the CodeQL database in `codeql/databases/server`.
  - Generate the analysis report in `codeql/reports/backend-results.sarif`.

### Frontend Analysis

Run the following command to analyze the frontend project (`dashboard`):

```bash
pnpm exec nx run dashboard:codeql
```

- This will:
  - Create the CodeQL database in `codeql/databases/dashboard`.
  - Generate the analysis report in `codeql/reports/frontend-results.sarif`.

---

## Customizing Output Locations

The SARIF report paths can be customized by modifying the `--output` flag in the respective `project.json` files.

Example:

```json
"--output=./custom/reports/dashboard-analysis.sarif"
```

---

## Adding `.gitignore` Rules

To prevent committing CodeQL databases but allow SARIF reports, add the following to your `.gitignore` file:

```plaintext
# Ignore CodeQL databases and reports
codeql/
```

---

## Verifying Integration

1. Run the Nx graph to ensure projects are configured correctly:

   ```bash
   pnpm exec nx graph
   ```

2. Execute the CodeQL targets and verify that the reports are generated in the correct locations:

   ```bash
   pnpm exec nx run server:codeql
   pnpm exec nx run dashboard:codeql
   ```

3. Open the SARIF files using a SARIF viewer (e.g., VSCode SARIF Viewer extension) or upload them to GitHub Advanced Security if applicable.

---

## Common Issues

### Database or Report Overwritten Unexpectedly

- Use unique output paths for each project.
- Ensure the `--overwrite` flag is used for the database creation command to avoid manual cleanup.
