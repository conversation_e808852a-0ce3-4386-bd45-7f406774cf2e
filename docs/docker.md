# Docker

## Local

Docker compose enables the execution of the images locally, so the frontend can link the backend, and the backend has a postgres database instance configured via environment variables. Docker compose setup the [communication between services][1] making them reachable with their service name (see [compose.yaml](../compose.yaml)).

In order to run the containers locally, you need Docker Desktop and run:

- Build the docker images: `nx run-many -t docker -all -c development`
- Run all the services locally: `docker compose up`

Then you can access the `frontend` via <http://localhost/> and the `backend` is also accessible via <http://localhost:3000/health/ping>.
The mapped routes are displayed in the compose log:

```log
...
backend-1 | [Nest] LOG [RouterExplorer] Mapped {/health/ping, GET} route +2ms
backend-1 | [Nest] LOG [RouterExplorer] Mapped {/health/database, GET} route +0ms
backend-1 | [Nest] LOG [RouterExplorer] Mapped {/health/disk, GET} route +1ms
backend-1 | [Nest] LOG [RouterExplorer] Mapped {/health/memory, GET} route +0ms
...
```

The `frontend` service proxies the `backend` under the `/api` location (see [nginx.conf](../src/frontend/dashboard/nginx.conf.tpl)):

```log
# requesting http://localhost/api/health/memory logs something this:
*1 "^/api(/.*)$" matches "/api/health/memory", client: **********, server: localhost, request: "GET /api/health/memory HTTP/1.1"
*1 rewritten data: "http://backend:3000/health/memory", args: "", client: **********, server: localhost
```

There is a specific `/ping` endpoint that is also redirected:

```log
# browsing http://localhost/ping logs:
*5 "^/ping$" matches "/ping", client: **********, server: localhost, request: "GET /ping HTTP/1.1"
*5 rewritten data: "http://backend:3000/health/ping", args: "", client: **********, server: localhost
```

### Troubleshooting

#### Cannot resolve backend

`nginx` needed the explicit resolution with `resolver 127.0.0.11;` after confirm the nameserver with `tail /etc/resolv.conf`.

#### Connection: Keep Alive

There were logs like `client timed out (110: Operation timed out) while waiting for request` because of [keep alive][2].

They were removed with `keepalive_requests 0; keepalive_timeout 0;` and should be overriden for a web-socked endpoint.

## Production

The configuration for the production artifacts is stored via environment variables via `.env.production` file.

[1]: https://docs.docker.com/compose/networking/#link-containers
[2]: https://nginx.nginx.narkive.com/hfFkEPbo/keepalive-and-connection-closed
