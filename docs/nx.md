# Nx Toolkit

Nx is a build system to maintain and scale monorepos. More info at [nx.dev][1],
it is like Angular CLI on steroids, without a single `angular.json` configuration file
but distributed `project.json` inside each package.

## Dependency Graph

You can open the existing dependency graph of the repo by running `pnpm nx graph`,
and clicking the "Show all projects" button.

By clicking a project bubble, you can see its details in the top-right icon.
There you can see the root folder, project type and available commands (or targets).
Learn more about [exploring the workspace][2]

## Commands / Targets / Tasks

Nx specify the configuration of each command inside the `project.json` file,
and some of them can have a distinct `development` and `production` configuration.

All the projects have a `lint` task, and most of them also have `build` and `test`.
They are executed by different plugins depending on the context. Learn more [in the docs][3].

## Docker Images

Only the Backend `server` and the Frontend `dashboard` have a configured `docker` task to generate `development` and `production` images.

Nx takes the [environment variables][4] from the `.env` and `.env.production` files respectively.

## CI

Nx takes care of execute the configured tasks ONLY in the changed (affected) packages
since the last GitHub Actions successful run. To see the configured jobs check [ci-cd.yml](../.github/workflows/ci-cd.yml)

## Common commands

Nx commands can be run directly by installing `nx` CLI globally:

```bash
pnpm add -g nx
```

and they have this format: `nx run $project:$target:$configuration` or `nx $target $project -c $configuration`.

Here a list of the usual commands used in this repo:

- pnpm nx serve dashboard
- pnpm nx run server:serve (this will run development with Swagger at <http://localhost:3000/api>)
- pnpm nx run-many -t test -all --verbose --max-workers 2
- pnpm nx run-many -t lint -all --fix --verbose --skip-nx-cache
- pnpm nx run-many -t build -all
- pnpm nx run-many -t serve
- pnpm nx run dashboard:e2e

## Troubleshoting

Sometimes the demon crashes and you need to run `nx reset` as the error message suggests.

[1]: https://nx.dev/
[2]: https://nx.dev/features/explore-graph
[3]: https://nx.dev/features/run-tasks
[4]: https://nx.dev/recipes/tips-n-tricks/define-environment-variables
