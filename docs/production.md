# Troubleshooting

## Docker image returning 500

When there's an error in production with no clear error message,
a fast way to reproduce the issue locally is to build the images locally:

```sh
nx run-many -t docker -all -c development
```

note that you will need Docker Desktop, and then run:

```sh
docker compose up
```

so you will have the dashboard at <http://localhost:4200> served from Docker
and the live logs of any endpoint not working at the moment,
so you just go to the failing point of the dashboard and reproduce the issue.
