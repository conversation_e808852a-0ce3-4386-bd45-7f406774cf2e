# Stripe

## Testing the webhook locally

After [installing the Stripe CLI][1] in your computer you must login to the project as explained in the documentation.

Then you will be able to [forward events to your local backend][2] with:

```sh
stripe listen --forward-to localhost:3000/stripe/webhook
```

and the CLI will popup a signing secret like `whsec_75fda*****` that you will need to put in the `.env` file
into the `STRIPE_WEBHOOK_SECRET_TEST` variable, **and then** you run the backend, because if you don't have the env
variable set before running `nx serve server` it won't take the secret correctly.

Now that you are listening events, you can play with the dashboard and see all the events triggered by <PERSON><PERSON>
and handled by the `StripeWebhook` service in the backend.

The list of [event types][3] and available [testing cards][4] triggers different responses like errors or fraud warnings, are available in the Stripe documentation.

## Webhook management

The webhook is registered [in the dashboard][5] and inside its page you can see the log of events and also reveal the Signing secret being used.

The production signing secret is stored in the `.env.production` file into the `STRIPE_WEBHOOK_SECRET` variable.

## Customer emails

There are [automated notifications][6] for the customers that should be configured in order to notify successful payments and correct failed payments,
so we avoid involuntary churn by giving the possibility to the user to update their payment method so it can be retried successfully.

[1]: https://docs.stripe.com/stripe-cli
[2]: https://docs.stripe.com/webhooks#local-listener
[3]: https://docs.stripe.com/api/events/types
[4]: https://docs.stripe.com/testing
[5]: https://dashboard.stripe.com/test/webhooks
[6]: https://docs.stripe.com/billing/revenue-recovery/customer-emails
