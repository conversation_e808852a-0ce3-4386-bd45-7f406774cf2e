{"name": "@neural/monorepo", "version": "0.1.0", "private": true, "license": "UNLICENSED", "scripts": {"format": "nx format:write", "sb:start": "supabase start", "sb:diff": "supabase db diff", "sb:dump": "supabase db dump --data-only > supabase/seed.sql", "sb:local": "supabase db dump --local --data-only > supabase/seed.sql", "sb:types": "supabase gen types typescript", "sb:stop": "supabase stop --no-backup", "sb:status": "supabase status", "sb:link": "supabase link", "sb:pull": "supabase db pull migration", "sb:migrate": "supabase migration up", "sb:push": "supabase db push", "prepare": "husky || true", "stripe:listen": "stripe listen --forward-to localhost:3000/stripe/webhook"}, "dependencies": {"@angular/animations": "18.1.2", "@angular/cdk": "18.1.2", "@angular/common": "18.1.2", "@angular/compiler": "18.1.2", "@angular/core": "18.1.2", "@angular/forms": "18.1.2", "@angular/localize": "^19.1.4", "@angular/material": "18.1.2", "@angular/platform-browser": "18.1.2", "@angular/platform-browser-dynamic": "18.1.2", "@angular/router": "18.1.2", "@ctrl/ngx-emoji-mart": "^9.2.0", "@formatjs/intl-displaynames": "^6.6.8", "@golevelup/nestjs-stripe": "0.8.0", "@google-cloud/logging-winston": "^6.0.0", "@google-cloud/pubsub": "^4.0.0", "@nebular/theme": "13.0.0", "@nestjs/axios": "^3.0.3", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "10.3.10", "@nestjs/config": "3.2.3", "@nestjs/core": "10.3.10", "@nestjs/passport": "10.0.3", "@nestjs/platform-express": "10.3.10", "@nestjs/platform-socket.io": "10.3.10", "@nestjs/schedule": "^4.1.0", "@nestjs/swagger": "^7.4.0", "@nestjs/terminus": "10.2.3", "@nestjs/typeorm": "10.0.2", "@nestjs/websockets": "10.3.10", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@ng-select/ng-select": "^14.2.0", "@ngrx/signals": "18.0.0", "@nx/angular": "19.5.3", "@popperjs/core": "^2.11.8", "@socket.io/redis-adapter": "^8.3.0", "@stripe/stripe-js": "^4.4.0", "@supabase/supabase-js": "2.44.4", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "angular-calendar": "^0.31.1", "apexcharts": "^3.53.0", "axios": "^1.7.7", "bootstrap": "5.3.3", "bull": "^4.16.5", "cache-manager": "^5.7.6", "chartist": "^1.3.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cron": "^3.1.7", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "feather-icons": "^4.29.2", "handlebars": "^4.7.8", "helmet": "^7.1.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lodash": "4.17.21", "multer": "1.4.5-lts.1", "nest-winston": "^1.9.7", "nestjs-supabase-auth": "^1.0.9", "ng-apexcharts": "^1.12.0", "ng-chartist": "^9.0.0", "ng-recaptcha-2": "^14.0.0", "ngx-mask": "^18.0.0", "ngx-owl-carousel-o": "^19.0.0", "ngx-stripe": "18.1.0", "ngx-toastr": "^19.0.0", "nodemailer": "^6.9.16", "normalize.css": "^8.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "puppeteer": "^22.15.0", "ramda": "^0.30.1", "reflect-metadata": "^0.2.2", "rxjs": "7.8.1", "sanitize-html": "^2.13.0", "sharp": "^0.33.5", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "ssh2": "^1.16.0", "stripe": "^14.25.0", "tslib": "^2.7.0", "typeorm": "^0.3.20", "winston": "^3.14.2", "winston-daily-rotate-file": "^5.0.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "18.1.2", "@angular-devkit/core": "18.1.2", "@angular-devkit/schematics": "18.1.2", "@angular-eslint/eslint-plugin": "18.1.0", "@angular-eslint/eslint-plugin-template": "18.1.0", "@angular-eslint/template-parser": "18.1.0", "@angular/compiler-cli": "18.1.2", "@angular/language-service": "18.1.2", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@darraghor/eslint-plugin-nestjs-typed": "^5.0.23", "@nestjs/schematics": "10.1.3", "@nestjs/testing": "10.3.10", "@nx-tools/container-metadata": "6.0.1", "@nx-tools/nx-container": "6.0.1", "@nx/cypress": "19.5.3", "@nx/devkit": "19.5.3", "@nx/eslint": "19.5.3", "@nx/eslint-plugin": "19.5.3", "@nx/jest": "19.5.3", "@nx/js": "19.5.3", "@nx/nest": "19.5.3", "@nx/node": "19.5.3", "@nx/web": "19.5.3", "@nx/webpack": "19.5.3", "@nx/workspace": "19.5.3", "@nxlv/python": "18.1.0", "@schematics/angular": "18.1.2", "@swc-node/register": "1.10.9", "@swc/core": "1.7.2", "@swc/helpers": "0.5.12", "@types/bull": "^4.10.4", "@types/feather-icons": "^4.29.4", "@types/jest": "^29.5.13", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^1.4.12", "@types/node": "^18.19.50", "@types/nodemailer": "^6.4.16", "@types/passport-jwt": "^4.0.1", "@types/ramda": "^0.30.2", "@types/sanitize-html": "^2.13.0", "@types/sharp": "^0.32.0", "@types/ssh2": "^1.15.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "7.17.0", "@typescript-eslint/parser": "7.17.0", "@typescript-eslint/utils": "7.17.0", "autoprefixer": "^10.4.20", "cypress": "^13.13.0", "dotenv": "16.4.5", "dotenv-expand": "11.0.6", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^2.13.4", "eslint-plugin-disable-autofix": "^4.3.0", "eslint-plugin-import": "^2.30.0", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-preset-angular": "14.2.0", "jsonc-eslint-parser": "^2.4.0", "lint-staged": "^15.2.10", "ng-mocks": "^14.13.1", "ng-packagr": "18.1.0", "nx": "19.5.3", "prettier": "^2.8.8", "supabase": "1.223.10", "ts-jest": "^29.2.5", "ts-node": "10.9.2", "typescript": "5.5.4", "uuid": "^10.0.0", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=18", "pnpm": "8.x"}, "pnpm": {"patchedDependencies": {"@supabase/auth-js@2.64.4": "patches/@<EMAIL>", "@nebular/theme@13.0.0": "patches/@<EMAIL>"}}, "resolutions": {"body-parser": "1.20.3", "path-to-regexp": "3.3.0", "express": "4.20.0", "send": "0.19.0", "webpack": ">=5.94.0", "rollup": ">=4.22.4", "vite": ">=5.3.6", "cookie": ">=0.7.0"}}