diff --git a/styles/core/theming/_get-value.scss b/styles/core/theming/_get-value.scss
index 8dae59de6d58917b451ace7c32809196028fa79d..fa72ddb67f7c0480a2bf92215718890ca60e5c01 100644
--- a/styles/core/theming/_get-value.scss
+++ b/styles/core/theming/_get-value.scss
@@ -59,7 +59,7 @@
   }
 
   @if ($value == null) {
-    @warn 'Nebular Theme: `nb-theme()` cannot find value for key `' + $key + '` for theme `'+ theming-variables.$nb-theme-name +'`';
+    @warn 'Nebular Theme: `nb-theme()` cannot find value for key `' + $key + '` for theme `'+ theming-variables.$nb-theme-name (+"`");
   }
 
   @return $value;
