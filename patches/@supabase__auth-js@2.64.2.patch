diff --git a/dist/main/GoTrueClient.js b/dist/main/GoTrueClient.js
index 93eae922617b27cf4bcd3f259b71a95b979935e0..872244acb81d88de927c239836f951643f19c911 100644
--- a/dist/main/GoTrueClient.js
+++ b/dist/main/GoTrueClient.js
@@ -87,9 +87,9 @@ class GoTrueClient {
         if (settings.lock) {
             this.lock = settings.lock;
         }
-        else if ((0, helpers_1.isBrowser)() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {
-            this.lock = locks_1.navigatorLock;
-        }
+        // else if ((0, helpers_1.isBrowser)() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {
+        //     this.lock = locks_1.navigatorLock;
+        // }
         else {
             this.lock = lockNoOp;
         }
diff --git a/dist/module/GoTrueClient.js b/dist/module/GoTrueClient.js
index e4630763d290d01b111819779e127b2fd8fe51dc..dd6bc235bf8d14156702e52cc24f92f6a6c9ed85 100644
--- a/dist/module/GoTrueClient.js
+++ b/dist/module/GoTrueClient.js
@@ -6,7 +6,7 @@ import { decodeJWTPayload, Deferred, getItemAsync, isBrowser, removeItemAsync, r
 import { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage';
 import { polyfillGlobalThis } from './lib/polyfills';
 import { version } from './lib/version';
-import { LockAcquireTimeoutError, navigatorLock } from './lib/locks';
+import { LockAcquireTimeoutError } from './lib/locks';
 polyfillGlobalThis(); // Make "globalThis" available
 const DEFAULT_OPTIONS = {
     url: GOTRUE_URL,
@@ -82,9 +82,9 @@ export default class GoTrueClient {
         if (settings.lock) {
             this.lock = settings.lock;
         }
-        else if (isBrowser() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {
-            this.lock = navigatorLock;
-        }
+        // else if (isBrowser() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {
+        //     this.lock = navigatorLock;
+        // }
         else {
             this.lock = lockNoOp;
         }
diff --git a/src/GoTrueClient.ts b/src/GoTrueClient.ts
index 7cb06f3a338ff10a204bfe1b91f8bfa5cc8f5368..0e9d18355d67eaecdb9822eabf38ca669c1ab06a 100644
--- a/src/GoTrueClient.ts
+++ b/src/GoTrueClient.ts
@@ -38,7 +38,7 @@ import {
 import { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage'
 import { polyfillGlobalThis } from './lib/polyfills'
 import { version } from './lib/version'
-import { LockAcquireTimeoutError, navigatorLock } from './lib/locks'
+import { LockAcquireTimeoutError } from './lib/locks'
 
 import type {
   AuthChangeEvent,
@@ -208,8 +208,8 @@ export default class GoTrueClient {
 
     if (settings.lock) {
       this.lock = settings.lock
-    } else if (isBrowser() && globalThis?.navigator?.locks) {
-      this.lock = navigatorLock
+    // } else if (isBrowser() && globalThis?.navigator?.locks) {
+    //   this.lock = navigatorLock
     } else {
       this.lock = lockNoOp
     }
