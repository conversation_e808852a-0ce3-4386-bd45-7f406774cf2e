import { Logger, MiddlewareConsumer, Module, NestModule, ValidationPipe } from '@nestjs/common';
import { APP_FILTER, APP_PIPE } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthModule } from './auth/auth.module';
import { ConfigurationModule } from './core/configs/configuration.module';
import { DatabaseModule } from './core/database/database.module';
import { HealthModule } from './core/health/health.module';
import { AllExceptionsFilter } from './core/logger/all-exceptions.filter';
import { CustomLoggerService } from './core/logger/custom-logger.service';
import { LoggerMiddleware } from './core/logger/logger.middleware';
import { SupabaseModule } from './core/supabase/supabase.module';
import { DeleteAccountModule } from './delete-account/delete-account.module';
import { EarningModule } from './earning/earning.module';
import { EmailModule } from './email/email.module';
import { GpuModule } from './gpu/gpu.module';
import { HelpModule } from './help/help.module';
import { InstanceIdModule } from './instance-id';
import { LeaderElectionModule } from './leader-election';
import { LogModule } from './log/log.module';
import { NotificationModule } from './notification/notification.module';
import { OutageRefundModule } from './outage-refund/outage-refund.module';
import { PaymentModule } from './payment/payment.module';
import { PubSubModule } from './pubsub';
import { RedisModule } from './redis';
import { ReportingModule } from './reporting/reporting.module';
import { RoleModule } from './roles/role.module';
import { SshModule } from './ssh/ssh.module';
import { TestModule } from './test/test.module';
import { UserModule } from './user/user.module';
import { UserAdminModule } from './user-admin/user-admin.module';

@Module({
  imports: [
    ConfigurationModule,
    ScheduleModule.forRoot(),
    AuthModule,
    DatabaseModule,
    HealthModule,
    SupabaseModule,
    GpuModule,
    HelpModule,
    LogModule,
    NotificationModule,
    PaymentModule,
    RoleModule,
    TestModule,
    UserModule,
    UserAdminModule,
    EarningModule,
    ReportingModule,
    DeleteAccountModule,
    InstanceIdModule,
    LeaderElectionModule,
    PubSubModule,
    RedisModule,
    EmailModule,
    SshModule,
    OutageRefundModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useFactory: async () =>
        new ValidationPipe({
          forbidUnknownValues: true,
          transform: true,
        }),
    },
    CustomLoggerService,
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: Logger,
      useClass: CustomLoggerService,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*'); // Apply to all routes
  }
}
