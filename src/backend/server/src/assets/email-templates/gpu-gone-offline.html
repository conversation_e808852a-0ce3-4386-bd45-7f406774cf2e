<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="dark light" />
    <meta name="supported-color-schemes" content="dark light" />
    <title>Update: GPU Offline</title>

    <style>
      :root {
        color-scheme: dark light;
      }

      body {
        background-color: #e7eff8;

        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
      }

      .email-container {
        background-color: #1b1b24;
        color: #e7eff8;
        width: 640px;
        border-radius: 16px;
        padding: 40px 32px;
        margin: 0 auto;
      }

      .email-content {
        width: 100%;
        text-align: left;
      }

      img {
        margin: auto;
      }

      hr {
        width: 576px;
        border: 1px solid #7a94f180;
      }

      h3 {
        font-family: Montserrat, sans-serif;
        font-size: 24px;
        font-weight: 500;
        color: #e7eff8;
      }

      .flex-row {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .text-content > p,
      li {
        font-family: Inter, sans-serif;
        font-size: 16px;
        font-weight: 400;
        color: #e7eff8;
      }

      .text-content {
        width: 576px;
        line-height: 24.4px;
        margin-bottom: 26px;
      }

      .text-content a {
        color: #e7eff8;
      }

      .email-footer {
        line-height: 16.8px;
        margin-top: 10px;
      }

      .email-footer > p {
        font-family: Inter, sans-serif;
        font-size: 14px;
        font-weight: 400;
        margin: 4px;
        color: #a5a5b3;
      }

      .email-footer a {
        color: #a5a5b3;
      }

      @media (prefers-color-scheme: dark) {
        body {
          background-color: #1b1b24 !important;
          color: #e7eff8;
        }

        .email-container {
          background-color: #1b1b24 !important;
        }

        .email-footer > p {
          color: #a5a5b3;
        }

        .email-footer a {
          color: #a5a5b3;
        }

        hr {
          border-color: #7a94f180;
        }
      }
    </style>
  </head>

  <body>
    <table class="email-container" cellpadding="0" cellspacing="0" border="0" align="center">
      <tbody>
        <tr>
          <td align="center">
            <img src="cid:unique@neuralinternet" class="flex-row" alt="Neural Internet" height="64" width="48" />
            <hr />
            <h3>Update: GPU Gone Offline</h3>
          </td>
        </tr>
        <tr>
          <td>
            <div class="text-content">
              <p style="font-family: Inter, sans-serif; font-size: 16px; font-weight: 400; color: #e7eff8">Hi {{userName}},</p>
              <p style="margin: 0; font-family: Inter, sans-serif; font-size: 16px; font-weight: 400; color: #e7eff8">
                We want to inform you that your allocated <span style="text-transform: uppercase">{{gpuModel}} </span>
                GPU:
                <span style="text-transform: uppercase; font-weight: bold">{{gpuName}}</span> has gone offline.
              </p>
              <p style="margin: 0; font-family: Inter, sans-serif; font-size: 16px; font-weight: 400; color: #e7eff8">
                Our team is actively monitoring the situation, and we will update you as soon as the GPU is back online.
              </p>
              <p>
                If you have any questions, please feel free to contact our support team at
                <a href="mailto:<EMAIL>" style="text-decoration: none; color: inherit"><EMAIL></a>.
              </p>
              <p>Thank you for using our services!</p>
              <hr />
              <tr class="email-footer">
                <td align="center">
                  <p style="font-family: Inter, sans-serif; font-size: 14px; font-weight: 400; margin: 4px; color: #a5a5b3">
                    &copy; {{currentYear}} Neural Internet. All rights reserved.
                  </p>
                  <p style="font-family: Inter, sans-serif; font-size: 14px; font-weight: 400; margin: 4px; color: #a5a5b3">
                    If you have any questions, contact us at
                    <a href="mailto:<EMAIL>" style="text-decoration: underline; color: #a5a5b3"><EMAIL></a>.
                  </p>
                </td>
              </tr>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </body>
</html>
