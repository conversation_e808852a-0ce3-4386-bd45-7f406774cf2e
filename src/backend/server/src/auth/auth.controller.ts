import { BadRequestException, Controller, Get, Query } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { UserService } from '../user/services/user.service';
import { CheckEmailDto } from './dto/check-email.dto';
import { CheckUsernameDto } from './dto/check-username.dto';
import { AuthService } from './services/auth.service';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService, private readonly userService: UserService) {}

  // eslint-disable-next-line @darraghor/nestjs-typed/api-methods-should-be-guarded
  @ApiOkResponse({ type: Boolean, description: 'Check if email or username is already taken' })
  @Get('validate')
  async checkUsername(@Query() query: CheckUsernameDto) {
    const { email, username } = query;
    if (email) {
      return await this.authService.checkEmail(email);
    } else if (username) {
      return await this.userService.checkUsername(username);
    }
    throw new BadRequestException('Invalid query parameters');
  }

  // eslint-disable-next-line @darraghor/nestjs-typed/api-methods-should-be-guarded
  @Get('validate-captcha')
  async checkCaptcha(@Query('token') token: string): Promise<boolean> {
    if (!token) {
      throw new BadRequestException('Token is required');
    }

    const result = await this.authService.validateCaptcha(token).toPromise();
    if (result === undefined) {
      throw new BadRequestException('CAPTCHA validation failed');
    }
    return result;
  }

  // eslint-disable-next-line @darraghor/nestjs-typed/api-methods-should-be-guarded
  @ApiOkResponse({ type: Boolean, description: 'Check if user with email exists' })
  @Get('validate-email-exists')
  async validateEmailExists(@Query() query: CheckEmailDto) {
    const { email } = query;
    if (!email) {
      throw new BadRequestException('Email is required');
    }
    return await this.authService.validateEmailExists(email);
  }
}
