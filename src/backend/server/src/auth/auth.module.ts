import { HttpModule } from '@nestjs/axios';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { SupabaseModule } from '../core';
import { UserModule } from '../user';
import { AuthController } from './auth.controller';
import { AuthService } from './services/auth.service';

@Module({
  imports: [UserModule, SupabaseModule, HttpModule],
  controllers: [AuthController],
  providers: [Logger, AuthService],
})
export class AuthModule {}
