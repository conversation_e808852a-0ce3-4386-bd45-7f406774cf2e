import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsOptional } from 'class-validator';
import { IsStringSanitized } from '../../shared/decorators/is-sanitized.decorator';

export class CheckUsernameDto {
  @ApiPropertyOptional({ description: 'Email address to check', type: String })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: 'Username to check', type: String })
  @IsOptional()
  @IsStringSanitized()
  username?: string;
}
