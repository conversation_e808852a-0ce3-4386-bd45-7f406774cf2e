import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { UserRole } from '@neural/models';
import type { Request } from 'express';
import { AuthGuard } from './auth.guard';

@Injectable()
export class AdminGuard extends AuthGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      await super.canActivate(context);

      const request: Request = context.switchToHttp().getRequest();
      const decoded = this.supabaseService.decodeToken(request.headers.authorization!);

      if (decoded.user_role !== UserRole.ADMIN) {
        throw new UnauthorizedException('User does not have admin privileges');
      }
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }

    return true;
  }
}
