import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import type { Request } from 'express';
import { SupabaseService } from '../../core/supabase/supabase.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(protected readonly supabaseService: SupabaseService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request: Request = context.switchToHttp().getRequest();
      const access_token = request.headers.authorization;

      if (!access_token) {
        throw new UnauthorizedException();
      }

      await this.supabaseService.verifyToken(access_token);
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }

    return true;
  }
}
