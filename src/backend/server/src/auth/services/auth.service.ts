import { HttpService } from '@nestjs/axios';
import { ConflictException, Injectable, BadRequestException, Logger, InternalServerErrorException, GoneException } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { EntityManager } from 'typeorm';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    @InjectEntityManager()
    readonly entityManager: EntityManager,
    private readonly http: HttpService,
  ) {}

  async checkEmail(email: string): Promise<boolean> {
    try {
      const user = await this.entityManager.query(`SELECT id, deleted_at FROM auth.users WHERE email = $1 LIMIT 1;`, [email]);
      if (user.length > 0) {
        if (user[0].deleted_at) {
          throw new ConflictException(
            'Your account has been deleted. You can recover it up to 30 days after deletion by contacting support.',
          );
        }
        throw new ConflictException('Email address already in use');
      }
      return true;
    } catch (error) {
      throw new ConflictException(error.message);
    }
  }

  validateCaptcha(token: string): Observable<boolean> {
    const url = `https://www.google.com/recaptcha/api/siteverify?secret=${process.env.GOOGLE_RECAPTCHA_SECRET_KEY}&response=${token}`;
    return this.http.post(url, {}).pipe(
      map((response) => {
        const data: { success: boolean } = response.data;
        if (typeof data.success !== 'boolean') {
          throw new BadRequestException('Invalid reCAPTCHA response');
        }
        return data.success;
      }),
    );
  }

  async validateEmailExists(email: string): Promise<boolean> {
    try {
      const user = await this.entityManager.query(`SELECT id FROM auth.users WHERE email = $1 LIMIT 1;`, [email]);

      if (user.length > 0) {
        return true;
      }

      throw new BadRequestException('User with email does not exist');
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      } else {
        this.logger.error('Error checking email existence', error);
        throw new InternalServerErrorException('An error occurred while validating the email. Please try again later.');
      }
    }
  }
}
