import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import database from './database.config';
import gpu from './gpu.config';
import smtp from './smtp.config';
import supabase from './supabase.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [database, gpu, supabase, smtp],
    }),
  ],
  exports: [ConfigModule],
})
export class ConfigurationModule {}
