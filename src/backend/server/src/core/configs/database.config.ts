import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export default registerAs('database', (): TypeOrmModuleOptions => {
  const options: TypeOrmModuleOptions = {
    type: 'postgres',
    host: process.env.DB_HOST,
    port: Number(process.env.DB_PORT),
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    autoLoadEntities: true,
    migrations: ['src/migrations/*.ts'],
    synchronize: false,
    ssl: {
      rejectUnauthorized: true,
      ca: process.env.SUPABASE_SSL_CERT,
    },
  };
  return options;
});
