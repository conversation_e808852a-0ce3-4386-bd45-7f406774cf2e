import { registerAs } from '@nestjs/config';

export default registerAs('gpu', () => {
  return {
    url: process.env.GPU_API_BASE_URL,
    pricePerRum: process.env.GPU_PRICE_PER_RAM,
    pricePerCpu: process.env.GPU_PRICE_PER_CPU,
    balanceMonitorIntervalInMinutes: process.env.BALANCE_MONITOR_INTERVAL_IN_MINUTES,
    balanceBufferTimeInMinutes: process.env.BALANCE_BUFFER_TIME_IN_MINUTES,
    gpuApiClientKey: process.env.GPU_API_CLIENT_KEY,
    gpuApiClientCert: process.env.GPU_API_CLIENT_CERT,
    gpuApiCaCert: process.env.GPU_API_CA_CERT,
  };
});
