/* eslint-disable @darraghor/nestjs-typed/api-method-should-specify-api-response */
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DiskHealthIndicator, HealthCheck, HealthCheckService, MemoryHealthIndicator, TypeOrmHealthIndicator } from '@nestjs/terminus';
import { AuthGuard } from '../../auth/guards';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly dbHealth: TypeOrmHealthIndicator,
    private readonly diskHealth: DiskHealthIndicator,
    private readonly memoryHealth: MemoryHealthIndicator,
  ) {}

  // eslint-disable-next-line @darraghor/nestjs-typed/api-methods-should-be-guarded
  @Get('ping')
  ping() {
    return 'pong';
  }

  @UseGuards(AuthGuard)
  @Get('database')
  @HealthCheck()
  checkDatabase() {
    return this.health.check([async () => this.dbHealth.pingCheck('typeorm')]);
  }

  @UseGuards(AuthGuard)
  @Get('disk')
  @HealthCheck()
  checkDisk() {
    return this.health.check([
      () =>
        this.diskHealth.checkStorage('storage', {
          path: '/',
          thresholdPercent: 0.8,
        }),
    ]);
  }

  @UseGuards(AuthGuard)
  @Get('memory')
  @HealthCheck()
  checkMemory() {
    return this.health.check([() => this.memoryHealth.checkHeap('memory_heap', 150 * 1024 * 1024)]);
  }
}
