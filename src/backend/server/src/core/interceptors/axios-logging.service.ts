import { HttpService } from '@nestjs/axios';
import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { AxiosResponse, InternalAxiosRequestConfig } from 'axios'; // Use InternalAxiosRequestConfig instead

@Injectable()
export class AxiosLoggingInterceptor {
  private readonly logger = new Logger(AxiosLoggingInterceptor.name);

  constructor(private readonly httpService: HttpService) {
    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Intercept outgoing requests and log them
    this.httpService.axiosRef.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        this.logRequest(config);
        return config;
      },
      (error) => {
        this.logRequestError(error);
        return Promise.reject(error);
      },
    );

    // Intercept incoming responses and log them
    this.httpService.axiosRef.interceptors.response.use(
      (response: AxiosResponse) => {
        this.logResponse(response);
        return response;
      },
      (error) => {
        this.logResponseError(error);
        return Promise.reject(error);
      },
    );
  }

  private logRequest(config: InternalAxiosRequestConfig) {
    try {
      this.logger.log(`[Request] : ${config.method?.toUpperCase()} ${config.url}`, config.data);
    } catch (error) {
      console.error('Logging error with message: ', error?.message || 'No message available');
    }
  }

  private logRequestError(error: any) {
    try {
      this.logger.error(`[Request Error]: ${error?.message || 'No message available'}`, error?.config || 'No config available');
    } catch (loggingError) {
      console.error('Logging request error with message: ', loggingError?.message || 'No message available');
    }
  }

  private logResponse(response: AxiosResponse) {
    try {
      this.logger.log(`[Response]: ${response.status} ${response.statusText} for ${response.config.url}`, response.data);
    } catch (error) {
      console.error('Logging error with message: ', error?.message || 'No message available');
    }
  }

  private logResponseError(error: any) {
    try {
      const url = error?.config?.url || 'unknown';
      const status = error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
      const message = `[Response Error] ${status} - ${error.message ?? ''} for ${url}`;

      this.logger.error(message, error?.stack || {}, {
        source: 'server',
        status: status,
        severity: 'ERROR',
        url: url,
        data: error?.response?.data,
        timestamp: new Date().toISOString(),
      });
    } catch (loggingError) {
      console.error('Logging response error with message: ', loggingError.message);
    }
  }
}
