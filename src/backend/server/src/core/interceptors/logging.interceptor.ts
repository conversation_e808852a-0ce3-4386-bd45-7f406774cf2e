import { CallH<PERSON>ler, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const method = request?.method || 'unknown_method';
    const url = request?.url || 'unknown_url';
    const { body, query, params } = request || {};
    const now = Date.now();

    try {
      // Skip logging for Stripe requests
      if (url.includes('/stripe')) {
        return next.handle();
      }

      // Log the request details
      this.logger.log(`[Request] : ${method} ${url}`, { body, query, params, method, url, timestamp: now });
    } catch (err) {
      this.fallbackLog('log', `[Request]: ${method} ${url}`, err, { body, query, params, method, url, timestamp: now });
    }

    return next.handle().pipe(
      tap({
        next: (data) => {
          try {
            const response = context.switchToHttp().getResponse();
            const { statusCode } = response || { statusCode: 'N/A' };
            const timeTaken = Date.now() - now;

            // Log the response details
            this.logger.log(`[Response]: ${method} ${url} ${statusCode} ${timeTaken}ms`, { data, statusCode, timeTaken, method, url });
          } catch (err) {
            this.fallbackLog('log', `[Response]: ${method} ${url}`, err, { data, method, url });
          }
        },
        error: (err) => {
          const logLevel = err.status >= 500 ? 'error' : err.status >= 400 ? 'warn' : 'log';
          try {
            if (logLevel === 'error') {
              this.logger.error(`Error processing ${method} ${url}`, err.stack, { error: err.message, method, url });
            } else if (logLevel === 'warn') {
              this.logger.warn(`Error processing ${method} ${url}`, { error: err.message, method, url });
            } else {
              this.logger.log(`Error processing ${method} ${url}`, { error: err.message, method, url });
            }
          } catch (logError) {
            this.fallbackLog(logLevel, `Error processing ${method} ${url}`, logError, {
              error: err.message,
              stack: err.stack,
              method,
              url,
            });
          }
        },
      }),
    );
  }

  private fallbackLog(level: 'log' | 'warn' | 'error', message: string, logError: Error, meta: Record<string, any>) {
    try {
      const severity = level.toUpperCase();
      console.error({
        message: `Logging failed: ${logError.message}`,
        originalMessage: message,
        severity: 'ERROR',
        originalSeverity: severity,
        originalMeta: meta,
      });
    } catch (e) {
      console.error('Logging failed.');
    }
  }
}
