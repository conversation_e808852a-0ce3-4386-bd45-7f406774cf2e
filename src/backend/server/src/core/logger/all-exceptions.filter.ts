import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ErrorResponse } from '@neural/models';
import { Request, Response } from 'express';
import Stripe from 'stripe';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: <PERSON><PERSON><PERSON>, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const { url } = request;
    const timestamp = new Date().toISOString();

    let status: number;
    let message: string | object;
    let stack: string | undefined;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      message = exception.getResponse();
      stack = undefined; // Reset stack trace for HttpException to avoid exposing sensitive details
    } else if (exception instanceof Stripe.errors.StripeError) {
      status = exception.statusCode || HttpStatus.BAD_REQUEST;
      message = exception.message;
      stack = exception.stack; // StripeError has its own stack trace
    } else {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = exception?.message;
      stack = exception?.stack;
    }

    try {
      message = typeof message !== 'string' ? (message as { message: string }).message ?? JSON.stringify(message) : message;
    } catch (error) {
      console.error('Error parsing exception message:', error.message || 'No message available');
    }
    // Log based on severity using conditional statements for GCP-compatible logging
    try {
      if (status >= 500) {
        this.logger.error(message, stack, { source: 'server', status, severity: 'ERROR', timestamp, url, method: request.method });
      } else if (status >= 400) {
        this.logger.warn(message, { source: 'server', status, severity: 'WARNING', timestamp, url, method: request.method });
      } else {
        this.logger.log(message, { source: 'server', status, severity: 'INFO', timestamp, url, method: request.method });
      }
    } catch (logError) {
      // Fallback logging to console.error in case the logger fails
      console.error(
        JSON.stringify({
          message: `Logging failed: ${logError.message}`,
          originalMessage: message,
          originalStack: stack,
          severity: 'ERROR', // Always log the fallback as an ERROR
          loggingFailureSeverity: status >= 500 ? 'ERROR' : status >= 400 ? 'WARNING' : 'INFO',
          source: 'server',
          status,
          timestamp,
          url,
          method: request.method,
        }),
      );
    }

    const errorResponse: ErrorResponse = {
      status,
      timestamp,
      url,
      message,
      stack,
    };

    // Send the response to the client
    response.status(status).json(errorResponse);
  }
}
