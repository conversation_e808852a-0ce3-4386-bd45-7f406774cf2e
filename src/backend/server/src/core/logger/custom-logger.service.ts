import { Injectable, LoggerService, Logger } from '@nestjs/common';
import { winstonLogger } from './winston.logger';

@Injectable()
export class CustomLoggerService extends Logger implements LoggerService {
  error(message: string, trace?: string, context?: string, metadata?: Record<string, unknown>) {
    const contextString = context ? JSON.stringify(context) : '';
    super.error(message, trace, contextString);
    winstonLogger.error(message, {
      stack: trace,
      ...metadata,
    });
  }

  log(message: string, context?: string, metadata?: Record<string, unknown>) {
    const contextString = context ? JSON.stringify(context) : '';
    super.log(message, contextString);
    winstonLogger.info(message, {
      ...metadata,
    });
  }

  warn(message: string, context?: string, metadata?: Record<string, unknown>) {
    const contextString = context ? JSON.stringify(context) : '';
    super.warn(message, contextString);
    winstonLogger.warn(message, {
      ...metadata,
    });
  }

  debug(message: string, context?: string, metadata?: Record<string, unknown>) {
    const contextString = context ? JSON.stringify(context) : '';
    super.debug(message, contextString);
    winstonLogger.debug(message, {
      ...metadata,
    });
  }

  verbose(message: string, context?: string, metadata?: Record<string, unknown>) {
    const contextString = context ? JSON.stringify(context) : '';
    super.verbose(message, contextString);
    winstonLogger.verbose(message, {
      ...metadata,
    });
  }
}
