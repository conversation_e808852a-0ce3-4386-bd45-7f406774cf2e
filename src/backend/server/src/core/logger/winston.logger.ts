import { LoggingWinston } from '@google-cloud/logging-winston';
import { createLogger, format, transports } from 'winston';
import 'winston-daily-rotate-file';

type EnvType = 'production' | 'staging' | 'dev' | 'local';

const customFormat = format.printf(({ level, message, label, timestamp, ms, stack, ...meta }) => {
  const metaString = Object.keys(meta)
    .map((key) => `${key}: ${JSON.stringify(meta[key])}`)
    .join(', ');
  return `${timestamp} [${label}] ${level} : ${message} ${ms} ${stack ? `${stack} ` : ''}${metaString ? `${metaString}` : ''}`;
});

// Custom format with color (for local environment)
const customFormatWithColor = format.combine(
  format.colorize(), // Apply colorization
  customFormat, // Use the same format as above
);

// Determine the current environment
const env: EnvType = (process.env.GPU_LOG_LEVEL as EnvType) || 'production';

// Map environments to log levels
const logLevelMapping: Record<EnvType, string> = {
  production: 'silly',
  staging: 'silly',
  dev: 'silly',
  local: 'silly', // Fallback for local or undefined environments
};

// Select the logger level based on the environment
const logLevel = logLevelMapping[env] || 'error'; // Default to 'error' if the environment is not defined in the mapping

// Conditionally select the format based on the environment
const loggerFormat = env === 'local' ? customFormatWithColor : customFormat;

const loggerTransports =
  env === 'local'
    ? [
        new transports.Console({
          level: logLevel,
        }),
      ]
    : [
        new transports.Console({
          level: logLevel,
        }),
        new LoggingWinston({
          logName: 'ni-gcp-logs',
          handleExceptions: true,
        }),
      ];

// Create and export the Winston logger instance
const winstonLogger = createLogger({
  format: format.combine(format.label({ label: 'ni-logs' }), format.timestamp(), format.ms(), format.errors({ stack: true }), loggerFormat),
  transports: loggerTransports,
});

// Export the logger for use in other parts of the application
export { winstonLogger };
