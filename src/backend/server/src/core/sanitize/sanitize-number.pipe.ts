import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class SanitizeNumberPipe implements PipeTransform {
  transform(value: unknown) {
    // Ensure the value is of type string or number
    if (typeof value !== 'string' && typeof value !== 'number') {
      throw new BadRequestException('Expected a string or number');
    }

    // Convert the value to a number
    const sanitizedValue = Number(value);

    // Check if the conversion was successful and the result is a finite number
    if (isNaN(sanitizedValue) || !isFinite(sanitizedValue)) {
      throw new BadRequestException('Invalid number input');
    }

    return sanitizedValue;
  }
}
