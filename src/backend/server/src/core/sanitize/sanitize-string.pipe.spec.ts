import { BadRequestException } from '@nestjs/common';
import { SanitizeStringPipe } from './sanitize-string.pipe';

describe('SanitizeStringPipe', () => {
  let pipe: SanitizeStringPipe;

  beforeEach(() => {
    pipe = new SanitizeStringPipe();
  });

  it('should throw BadRequestException if input is not a string', () => {
    expect(() => pipe.transform(123)).toThrow(BadRequestException);
    expect(() => pipe.transform({})).toThrow(BadRequestException);
    expect(() => pipe.transform(null)).toThrow(BadRequestException);
  });

  it('should sanitize string by removing HTML tags', () => {
    const result = pipe.transform('<script>alert("xss")</script>Hello');
    expect(result).toEqual('Hello');
  });

  it('should throw BadRequestException if result is empty after sanitization', () => {
    expect(() => pipe.transform('<script>alert("xss")</script>')).toThrow(BadRequestException);
  });

  it('should return the sanitized string when valid', () => {
    const result = pipe.transform('Hello <b>World</b>');
    expect(result).toEqual('Hello World');
  });

  // Add more tests for edge cases as needed
});
