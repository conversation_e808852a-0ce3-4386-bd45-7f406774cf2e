import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import sanitizeHtml from 'sanitize-html';

@Injectable()
export class SanitizeStringPipe implements PipeTransform {
  transform(value: unknown) {
    if (typeof value !== 'string') {
      throw new BadRequestException('Expected a string');
    }

    // Sanitize the input to remove unwanted characters
    const sanitizedValue = sanitizeHtml(value, {
      allowedTags: [],
      allowedAttributes: {},
    });

    if (!sanitizedValue) {
      throw new BadRequestException('Invalid input after sanitization');
    }

    return sanitizedValue;
  }
}
