import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SupabaseClient } from '@supabase/supabase-js';
import { SupabaseService } from './supabase.service';
import { SUPABASE_CLIENT } from './supabase.token';

@Module({
  providers: [
    {
      provide: SUPABASE_CLIENT,
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const url = configService.get<string>('supabase.url');
        const apiKey = configService.get<string>('supabase.apiKey');

        if (!url || !apiKey) {
          throw new Error('Provide Supabase`s options.');
        }
        return new SupabaseClient(url, apiKey);
      },
    },
    SupabaseService,
  ],
  exports: [SupabaseService],
})
export class SupabaseModule {}
