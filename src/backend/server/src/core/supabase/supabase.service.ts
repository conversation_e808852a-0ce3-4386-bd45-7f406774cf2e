import { Inject, Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { IUserCached, AVATAR_BUCKET, getUserFromAccount, softDeletedUser, IUploadResponseData } from '@neural/models';
import { SupabaseClient } from '@supabase/supabase-js';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { SUPABASE_CLIENT } from './supabase.token';

@Injectable()
export class SupabaseService {
  private readonly logger = new Logger(SupabaseService.name);

  private indexCache = new Map<string, string>(); // user_id : token
  private usersCache = new Map<string, IUserCached>(); // token : User

  constructor(
    @Inject(SUPABASE_CLIENT)
    private readonly supabaseClient: SupabaseClient,
  ) {}

  decodeToken(token: string) {
    return jwt.decode(token.replace('Bearer ', '')) as JwtPayload;
  }

  async verifyToken(token: string) {
    // check if User is cached
    if (this.usersCache.has(token)) {
      const user = this.usersCache.get(token)!;
      if (user.deleted_at) {
        throw new UnauthorizedException('This user has been deleted.');
      }
      return user;
    }

    const { data, error } = await this.supabaseClient.auth.getUser(token.replace('Bearer ', ''));

    if (error) {
      throw new UnauthorizedException(error.message);
    }

    const authUser = data.user;
    const userFromSupabase = authUser as softDeletedUser;
    if (userFromSupabase.deleted_at) {
      throw new UnauthorizedException('This user has been deleted.');
    }

    const { user_role } = this.decodeToken(token);
    const user = getUserFromAccount(data.user, user_role);

    // remove expired tokens
    if (this.indexCache.has(user.user_id)) {
      this.usersCache.delete(this.indexCache.get(user.user_id)!);
    }

    this.indexCache.set(user.user_id, token);
    this.usersCache.set(token, user);

    return user;
  }

  async createUser(
    email: string,
    password: string,
    first_name: string | undefined,
    last_name: string | undefined,
    username: string | undefined,
    roles: string[] | undefined,
  ) {
    try {
      const { data, error } = await this.supabaseClient.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          first_name,
          last_name,
          username,
        },
        app_metadata: {
          roles,
        },
      });

      if (error) {
        throw new Error('Error creating user: ' + error.message);
      }

      return data.user;
    } catch (error) {
      throw new Error('Unexpected error creating user: ' + error.message);
    }
  }

  async uploadAvatar(fileBuffer: Buffer, fileName: string): Promise<IUploadResponseData> {
    const { data, error } = await this.supabaseClient.storage.from(AVATAR_BUCKET).upload(fileName, fileBuffer, {
      cacheControl: '3600',
      upsert: false,
    });
    if (error) {
      throw new Error('File upload failed: ' + error.message);
    }
    return data;
  }

  async deleteAvatar(filePath: string): Promise<void> {
    try {
      const { error } = await this.supabaseClient.storage.from(AVATAR_BUCKET).remove([filePath]);

      if (error) {
        throw new Error('Failed to delete old avatar: ' + error.message);
      }
    } catch (error) {
      this.logger.error('Error deleting old avatar:', error.message);
      throw new Error('Old avatar deletion process failed: ' + error.message);
    }
  }

  async generateSignedAvatarUrl(avatar_url: string): Promise<string> {
    try {
      const { data, error } = await this.supabaseClient.storage.from(AVATAR_BUCKET).createSignedUrl(avatar_url, 60 * 60); // 1-hour expiration

      if (error) {
        this.logger.error('Failed to generate signed URL for avatar:', error);
        throw new Error('Failed to generate signed URL.');
      }

      return data?.signedUrl; // Return the signed URL if successful, otherwise null
    } catch (error) {
      this.logger.error('Error in generateSignedAvatarUrl:', error);
      throw new Error('Signed URL generation failed: ' + error.message);
    }
  }
}
