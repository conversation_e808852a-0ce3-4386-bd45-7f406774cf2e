import { Controller, Delete, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import type { Request } from 'express';
import { AuthGuard } from '../auth';
import { SupabaseService } from '../core';
import { DeleteAccountService } from './services/delete-account.service';

@ApiTags('delete-account')
@Controller('delete-account')
export class DeleteAccountController {
  constructor(private readonly supabaseService: SupabaseService, private readonly deleteAccountService: DeleteAccountService) {}

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Delete()
  public async deleteAccount(@Req() request: Request) {
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    return this.deleteAccountService.deleteUser(user_id, user_id);
  }
}
