import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SupabaseModule } from '../core';
import { GpuModule } from '../gpu';
import { PaymentModule } from '../payment';
import { SharedModule } from '../shared';
import { User } from '../user';
import { DeleteAccountController } from './delete-account.controller';
import { DeleteAccountService } from './services/delete-account.service';

@Module({
  imports: [TypeOrmModule.forFeature([User]), PaymentModule, SupabaseModule, SharedModule, GpuModule],
  controllers: [DeleteAccountController],
  providers: [Logger, DeleteAccountService],
  exports: [DeleteAccountService],
})
export class DeleteAccountModule {}
