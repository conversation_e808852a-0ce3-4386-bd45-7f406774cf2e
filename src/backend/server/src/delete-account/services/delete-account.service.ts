import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { IGpusRented, IUserAdminRaw } from '@neural/models';
import Strip<PERSON> from 'stripe';
import { EntityManager, QueryRunner, Repository } from 'typeorm';
import { SupabaseService } from '../../core';
import { GpuService } from '../../gpu';
import { PaymentService } from '../../payment';
import { User } from '../../user';

@Injectable()
export class DeleteAccountService {
  private readonly logger = new Logger(DeleteAccountService.name);

  constructor(
    @InjectEntityManager()
    readonly entityManager: EntityManager,
    @InjectRepository(User)
    readonly userRepository: Repository<User>,
    readonly supabaseService: SupabaseService,
    readonly gpuService: GpuService,
    readonly paymentService: PaymentService,
  ) {}

  public async deleteUser(user_id: string, deleted_by: string) {
    try {
      const queryRunner = this.entityManager.connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        const userQuery = 'SELECT u.* FROM public.users u WHERE u.user_id = $1 AND u.deleted_at IS NULL';
        const [user]: IUserAdminRaw[] = await queryRunner.manager.query(userQuery, [user_id]);

        if (!user) {
          throw new NotFoundException('User not found');
        }

        const userAdminQuery = 'SELECT u.id FROM auth.users u WHERE u.id = $1 AND u.deleted_at IS NULL';
        const [adminUser]: IUserAdminRaw[] = await queryRunner.manager.query(userAdminQuery, [deleted_by]);

        if (!adminUser) {
          throw new NotFoundException('User (admin) not found');
        }

        // get all gpu instances rented by the user
        const gpuQuery = `SELECT g.gpu_rented_id, g.is_allocated FROM public.gpus_rented g WHERE g.user_id = $1 AND g.is_allocated = true`;
        const gpuInstances: IGpusRented[] = await queryRunner.manager.query(gpuQuery, [user_id]);

        // user gpu service to deallocate each gpu
        for (const gpu of gpuInstances) {
          await this.gpuService.deallocateGpuResource(user_id, gpu.gpu_rented_id);
        }

        // Refund user for the remaining balance
        if (user.stripe_account_id) {
          const freshUser = await queryRunner.manager.findOne(User, {
            where: { user_id: user_id },
          });
          if (!freshUser) {
            throw new NotFoundException('User not found');
          }
          const roundedBalanceInCents = Number((Math.round(freshUser.balance) / 100).toFixed(2)) * 100;
          const refundsDone: Array<Stripe.Refund> = await this.paymentService.refund(freshUser?.user_id, roundedBalanceInCents);
          // Update user balance (without transaction)
          const totalRefundsDone = refundsDone.reduce((total, refund) => (total = total + refund.amount), 0);
          if (totalRefundsDone) {
            await this.updateUserBalanceWithRetry(user_id, roundedBalanceInCents - totalRefundsDone, 3, 1000);
          }
        }

        // soft delete from `auth.users` table
        await queryRunner.manager.query('UPDATE auth.users SET deleted_at = NOW() WHERE id = $1', [user_id]);

        // soft delete from `public.users` table
        await queryRunner.manager.query('UPDATE public.users SET deleted_at = NOW(), deleted_by = $2 WHERE user_id = $1', [
          user_id,
          deleted_by,
        ]);

        await queryRunner.commitTransaction();
        // the user is deleted with the Postgres handle_delete_user trigger in auth.users

        return { user_id };
      } catch (error) {
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      throw new InternalServerErrorException('Error deleting user: ' + (error.message || error));
    }
  }

  private async updateUserBalanceWithRetry(
    userId: string,
    newBalance: number,
    retries = 3,
    delay = 1000,
    queryRunner?: QueryRunner,
  ): Promise<void> {
    while (retries > 0) {
      try {
        if (queryRunner) {
          await queryRunner.manager.update(User, { user_id: userId }, { balance: newBalance });
        } else {
          await this.userRepository.update({ user_id: userId }, { balance: newBalance });
        }
        this.logger.log(`Balance updated successfully for user ${userId} to ${newBalance}`);
        return; // Exit the function if the update succeeds
      } catch (error) {
        this.logger.warn(`Failed to update balance for user ${userId}. Retries left: ${retries - 1}. New balance: ${newBalance}`);
        retries -= 1;
        if (retries === 0) {
          this.logger.error(
            `Failed to update balance for user ${userId} after multiple attempts. The new balance should be ${newBalance}. Error: ${error.message}`,
          );
          throw new InternalServerErrorException('Failed to update user balance after refund');
        }

        // delay before retrying (optional)
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }
}
