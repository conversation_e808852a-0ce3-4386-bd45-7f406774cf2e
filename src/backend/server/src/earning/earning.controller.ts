import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AdminGuard } from '../auth';
import { GetEarningDto } from './dto/get-earning-data.dto';
import { EarningService } from './service/earning.service';

@ApiTags('earnings')
@Controller('earnings')
export class EarningController {
  constructor(private readonly earningService: EarningService) {}

  @ApiBearerAuth()
  @UseGuards(AdminGuard)
  @Get('earning-data')
  async getEarnings(@Query() query: GetEarningDto) {
    return this.earningService.getEarnings(query.period);
  }

  @ApiBearerAuth()
  @UseGuards(AdminGuard)
  @Get('cumulative-earning')
  async getCumulativeEarnings() {
    return this.earningService.getCumulativeEarnings();
  }
}
