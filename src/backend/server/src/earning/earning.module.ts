import { <PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SupabaseModule } from '../core/supabase/supabase.module';
import { EarningController } from './earning.controller';
import { GpusEarning } from './entities';
import { EarningService } from './service/earning.service';

@Module({
  imports: [TypeOrmModule.forFeature([GpusEarning]), SupabaseModule],
  controllers: [EarningController],
  providers: [EarningService, Logger],
  exports: [EarningService],
})
export class EarningModule {}
