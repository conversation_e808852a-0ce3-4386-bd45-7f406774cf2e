import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  IPeriod,
  ICostState,
  generateSerie,
  periodToDays,
  IGpusEarning,
  ICumulativeEarningsResponse,
  getStartAndEndOfDay,
  getTheEndOfDay,
  getTheStartOfDay,
  DATE_FORMAT,
} from '@neural/models';
import { format, subDays } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { And, Between, LessThanOrEqual, MoreThanOrEqual, Repository } from 'typeorm';
import { GpusEarning } from '../entities';

@Injectable()
export class EarningService {
  constructor(
    @InjectRepository(GpusEarning)
    private readonly gpuEarningRepository: Repository<GpusEarning>,
    private readonly logger: Logger,
  ) {}

  async getEarnings(period: IPeriod): Promise<ICostState> {
    try {
      const now = new Date();
      const days = periodToDays(period) - 1;
      const startDate = getTheStartOfDay(subDays(now, days));
      const endDate = getTheEndOfDay(now);

      const gpuEarnings = await this.gpuEarningRepository.find({
        where: {
          date_earning: And(MoreThanOrEqual(startDate), LessThanOrEqual(endDate)),
        },
        order: { date_earning: 'ASC' },
      });

      if (gpuEarnings.length === 0) {
        const defaultGpuEarnings = Array.from({ length: days + 1 }, (_, i) => {
          const date = new Date(startDate);
          date.setDate(startDate.getDate() + i);
          return { date, earning: 0 };
        });

        const data: [number, number][] = defaultGpuEarnings.map((entry) => {
          return [entry.date.getTime(), entry.earning];
        });
        return generateSerie(period, data);
      }

      const earningData = Array.from({ length: days + 1 }, (_, i) => {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);

        const earningEntry = gpuEarnings.filter((entry) => {
          const entryDate = new Date(entry.date_earning);
          entryDate.setHours(0, 0, 0, 0);
          return entryDate.getTime() === date.getTime();
        });

        return { date, earning: earningEntry.reduce((acc, entry) => acc + entry.earning, 0) };
      });

      const data: [number, number][] = earningData.map((entry) => {
        return [entry.date.getTime(), entry.earning];
      });

      return generateSerie(period, data);
    } catch (error) {
      throw new InternalServerErrorException('Error while fetching earnings');
    }
  }

  async createOrUpdateEarning(dateIn: Date, earning: number) {
    const today = toZonedTime(new Date(format(dateIn, DATE_FORMAT)), 'UTC');
    try {
      await this.gpuEarningRepository.query(
        `
        INSERT INTO gpus_earning (date_earning, earning)
        VALUES ($1, $2)
        ON CONFLICT (date_earning)
        DO UPDATE SET
          earning = gpus_earning.earning + EXCLUDED.earning;
        `,
        [today, earning],
      );
    } catch (error) {
      this.logger.error(`Failed to upsert earning. Date: ${today.toISOString()}. ${error.message}`);
    }
  }

  async getCumulativeEarnings(): Promise<ICumulativeEarningsResponse> {
    try {
      const query = this.gpuEarningRepository
        .createQueryBuilder('gpus_earning')
        .select('MIN(date_earning)', 'lastDate')
        .addSelect('SUM(earning)', 'earning')
        .getRawOne();

      const result = await query;

      return {
        lastDate: result.lastDate || new Date(),
        earning: result.earning || 0,
      };
    } catch (error) {
      throw new InternalServerErrorException('Error while fetching cumulative earnings');
    }
  }
}
