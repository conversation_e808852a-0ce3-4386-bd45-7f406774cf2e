import { Inject, Logger, Injectable } from '@nestjs/common';
import {
  AUTO_TOP_UP_EMAIL_QUEUE_JOB_NAME,
  EXTERNAL_DEALLOCATION_EMAIL_QUEUE_JOB_NAME,
  UN_PRICED_GPU_FOUND_EMAIL_QUEUE_JOB_NAME,
  SearchGpuData,
  PAYMENT_TOP_UP_EMAIL_QUEUE_JOB_NAME,
  GPU_BACK_ONLINE_EMAIL_QUEUE_JOB_NAME,
  OUTRAGE_REFUND_EMAIL_QUEUE_JOB_NAME,
  GPU_GONE_OFFLINE_EMAIL_QUEUE_JOB_NAME,
} from '@neural/models';
import { Queue as BullQueue } from 'bull';
import { EMAIL_QUEUE_TOKEN } from './email-token';
import { EmailService } from './email.service';

@Injectable()
export class EmailJobService {
  constructor(
    private readonly emailService: EmailService,
    @Inject(EMAIL_QUEUE_TOKEN) private emailQueue: BullQueue | null,
    private readonly logger: Logger,
  ) {}
  async sendAutoTopUpAuthorizationEmail(userEmail: string, name: string, authorizationLink: string) {
    if (!this.emailQueue) {
      this.logger.warn(`Email queue is not initialized. Sending ${AUTO_TOP_UP_EMAIL_QUEUE_JOB_NAME} email directly.`);
      return this.emailService.sendAutoTopUpAuthorizationEmail(userEmail, name, authorizationLink);
    }
    if (this.emailQueue.client.status !== 'ready') {
      this.logger.warn(`Email queue is not initialized. Sending ${AUTO_TOP_UP_EMAIL_QUEUE_JOB_NAME} email directly.`);
      return this.emailService.sendAutoTopUpAuthorizationEmail(userEmail, name, authorizationLink);
    }
    const job = await this.emailQueue.add(AUTO_TOP_UP_EMAIL_QUEUE_JOB_NAME, {
      userEmail,
      name,
      authorizationLink,
    });
    this.logger.log(`Added auto top-up email job to the queue. Job ID: ${job.id} , Job Name: ${job.name}`);
    return { jobId: job.id };
  }
  async sendExternalDeallocationEmail(userEmail: string, userName: string, gpuName: string, gpuModel: string, deallocatedAt: Date) {
    if (!this.emailQueue) {
      this.logger.warn(`Email queue is not initialized. Sending ${EXTERNAL_DEALLOCATION_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendExternalDeallocationEmail(userEmail, userName, gpuName, gpuModel, deallocatedAt);
    }
    if (this.emailQueue.client.status !== 'ready') {
      this.logger.warn(`Email queue is not ready. Sending ${EXTERNAL_DEALLOCATION_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendExternalDeallocationEmail(userEmail, userName, gpuName, gpuModel, deallocatedAt);
    }
    const job = await this.emailQueue.add(EXTERNAL_DEALLOCATION_EMAIL_QUEUE_JOB_NAME, {
      userEmail,
      userName,
      gpuName,
      gpuModel,
      deallocatedAt,
    });
    this.logger.log(`Added external deallocation email job to the queue. Job ID: ${job.id}, Job Name: ${job.name}`);
    return { jobId: job.id };
  }
  async sendUnpricedGpuFoundEmail(userEmail: string, name: string, gpusAvailable: SearchGpuData[]) {
    if (!this.emailQueue) {
      this.logger.warn(`Email queue is not initialized. Sending ${UN_PRICED_GPU_FOUND_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendAvailableGpusEmail(userEmail, name, gpusAvailable);
    }
    if (this.emailQueue.client.status !== 'ready') {
      this.logger.warn(`Email queue is not ready. Sending ${UN_PRICED_GPU_FOUND_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendAvailableGpusEmail(userEmail, name, gpusAvailable);
    }
    const job = await this.emailQueue.add(UN_PRICED_GPU_FOUND_EMAIL_QUEUE_JOB_NAME, {
      userEmail,
      name,
      gpusAvailable,
    });
    this.logger.log(`Added unpriced GPU email job to the queue. Job ID: ${job.id}, Job Name: ${job.name}`);
    return { jobId: job.id };
  }

  async sendPaymentTopUpEmail(userName: string, userEmail: string, balance: string, amount: string) {
    if (!this.emailQueue) {
      this.logger.warn(`Email queue is not initialized. Sending ${PAYMENT_TOP_UP_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendPaymentTopupNotification(userName, userEmail, balance, amount);
    }
    if (this.emailQueue.client.status !== 'ready') {
      this.logger.warn(`Email queue is not ready. Sending ${PAYMENT_TOP_UP_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendPaymentTopupNotification(userName, userEmail, balance, amount);
    }
    const job = await this.emailQueue.add(PAYMENT_TOP_UP_EMAIL_QUEUE_JOB_NAME, {
      userName,
      userEmail,
      balance,
      amount,
    });
    this.logger.log(`Added payment top-up email job to the queue. Job ID: ${job.id}, Job Name: ${job.name}`);
    return { jobId: job.id };
  }

  async sendGpuBackOnlineEmail(userEmail: string, userName: string, gpuName: string, gpuModel: string) {
    if (!this.emailQueue) {
      this.logger.warn(`Email queue is not initialized. Sending ${GPU_BACK_ONLINE_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendGpuBackOnlineEmail(userEmail, userName, gpuName, gpuModel);
    }
    if (this.emailQueue.client.status !== 'ready') {
      this.logger.warn(`Email queue is not ready. Sending ${GPU_BACK_ONLINE_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendGpuBackOnlineEmail(userEmail, userName, gpuName, gpuModel);
    }
    const job = await this.emailQueue.add(GPU_BACK_ONLINE_EMAIL_QUEUE_JOB_NAME, {
      userEmail,
      userName,
      gpuName,
      gpuModel,
    });
    this.logger.log(`Added GPU back ONLINE email job to the queue. Job ID: ${job.id}, Job Name: ${job.name}`);
    return { jobId: job.id };
  }

  async sendOutrageRefundEmail(
    userName: string,
    userEmail: string,
    refundAmount: string,
    outageStart: string,
    outageEnd: string,
    percentage: string,
  ) {
    if (!this.emailQueue) {
      this.logger.warn(`Email queue is not initialized. Sending outrage refund email directly.`);
      return this.emailService.sendOutrageRefundEmail(userName, userEmail, refundAmount, outageStart, outageEnd, percentage);
    }

    if (this.emailQueue.client.status !== 'ready') {
      this.logger.warn(`Email queue is not ready. Sending outrage refund email directly.`);
      return this.emailService.sendOutrageRefundEmail(userName, userEmail, refundAmount, outageStart, outageEnd, percentage);
    }

    const job = await this.emailQueue.add(OUTRAGE_REFUND_EMAIL_QUEUE_JOB_NAME, {
      userName,
      userEmail,
      refundAmount,
      outageStart,
      outageEnd,
      percentage,
    });

    this.logger.log(`Added outrage refund email job to the queue. Job ID: ${job.id}, Job Name: ${job.name}`);

    return { jobId: job.id };
  }

  async sendGpuGoneOfflineEmail(userEmail: string, userName: string, gpuName: string, gpuModel: string) {
    if (!this.emailQueue) {
      this.logger.warn(`Email queue is not initialized. Sending ${GPU_GONE_OFFLINE_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendGpuGoneOfflineEmail(userEmail, userName, gpuName, gpuModel);
    }
    if (this.emailQueue.client.status !== 'ready') {
      this.logger.warn(`Email queue is not ready. Sending ${GPU_GONE_OFFLINE_EMAIL_QUEUE_JOB_NAME}  email directly.`);
      return this.emailService.sendGpuGoneOfflineEmail(userEmail, userName, gpuName, gpuModel);
    }
    const job = await this.emailQueue.add(GPU_GONE_OFFLINE_EMAIL_QUEUE_JOB_NAME, {
      userEmail,
      userName,
      gpuName,
      gpuModel,
    });
    this.logger.log(`Added GPU gone OFFLINE email job to the queue. Job ID: ${job.id}, Job Name: ${job.name}`);
    return { jobId: job.id };
  }
}
