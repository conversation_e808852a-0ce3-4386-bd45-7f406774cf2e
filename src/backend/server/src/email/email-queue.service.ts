import { Inject, Injectable, Logger } from '@nestjs/common';
import { Queue as BullQueue } from 'bull';
import { EMAIL_QUEUE_TOKEN } from './email-token';

@Injectable()
export class EmailQueueService {
  private readonly logger = new Logger(EmailQueueService.name);
  constructor(@Inject(EMAIL_QUEUE_TOKEN) private emailQueue: BullQueue | null) {
    if (!this.emailQueue) {
      this.logger.warn('Email queue is not initialized.');
      return;
    }
    const redisClient = this.emailQueue.client;
    redisClient.on('error', (err) => {
      this.logger.error('Redis error:', err.message);
    });
    redisClient.on('connect', () => {
      this.logger.log('Connected to Redis');
    });
    redisClient.on('reconnecting', () => {
      this.logger.log('Reconnecting to Redis...');
    });
    this.emailQueue.on('completed', (job) => {
      this.logger.log(`Email job completed. Job ID: ${job.id}, Job Name: ${job.name}`);
    });
    this.emailQueue.on('failed', (job, error) => {
      this.logger.error(`Email job failed. Job ID: ${job.id}, Job Name: ${job.name}, Error: ${error.message}`, error.stack);
    });
    this.emailQueue.on('stalled', (job) => {
      this.logger.warn(`Email job stalled. Job ID: ${job.id}, Job Name: ${job.name}`);
    });
    this.emailQueue.on('active', (job) => {
      this.logger.log(`Email job is active. Job ID: ${job.id}, Job Name: ${job.name}`);
    });
    this.emailQueue.on('delayed', (job) => {
      this.logger.log(`Email job is delayed. Job ID: ${job.id}, Job Name: ${job.name}`);
    });
    this.emailQueue.on('waiting', (jobId) => {
      this.logger.log(`Email job is waiting in the queue. Job ID: ${jobId}`);
    });
    this.logger.log(`Email queue "${EMAIL_QUEUE_TOKEN}" initialized successfully.`);
  }
}
