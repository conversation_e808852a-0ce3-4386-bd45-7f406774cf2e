import { Lo<PERSON>, <PERSON>du<PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ISMTPConfig } from '@neural/models';
import Bull, { Queue as BullQueue } from 'bull';
import { EmailJobService } from './email-job.service';
import { EmailQueueService } from './email-queue.service';
import { EMAIL_QUEUE_TOKEN, SMTP_CONFIG_TOKEN } from './email-token';
import { EmailProcessor } from './email.processor';
import { EmailService } from './email.service';
import { EmailTransportService } from './mail.service';

@Module({
  providers: [
    {
      provide: SMTP_CONFIG_TOKEN,
      inject: [ConfigService, Logger],
      useFactory: async (configService: ConfigService, logger: Logger): Promise<ISMTPConfig | null> => {
        const host = configService.get<string>('smtp.host');
        const port = Number(configService.get<string>('smtp.port'));
        const secure = configService.get<string>('smtp.secure');
        const auth_user = configService.get<string>('smtp.auth_user');
        const auth_pass = configService.get<string>('smtp.auth_pass');
        const from = configService.get<string>('smtp.from');

        if (!host || !port || !secure || !auth_user || !auth_pass || !from) {
          logger.warn('SMTP configuration is missing. Emails will not be sent.');
          return null;
        }

        return {
          host,
          port,
          secure: secure === 'true',
          auth: {
            user: auth_user,
            pass: auth_pass,
          },
          from,
        };
        return null;
      },
    },
    {
      provide: EMAIL_QUEUE_TOKEN,
      inject: [Logger],
      useFactory: async (logger: Logger): Promise<BullQueue | null> => {
        const redisHost = process.env.REDIS_HOST_IP;
        const redisPort = process.env.REDIS_HOST_PORT;
        if (!redisHost || !redisPort) {
          logger.warn('Redis environment variables are missing. Email queue will not be initialized. Emails will be sent directly.');
          return null;
        }
        // return new Bull(EMAIL_QUEUE_TOKEN, {
        //   redis: {
        //     host: redisHost,
        //     port: parseInt(redisPort, 10),
        //   },
        // });
        return null;
      },
    },
    EmailTransportService,
    EmailJobService,
    EmailProcessor,
    EmailQueueService,
    EmailService,
    Logger,
  ],
  exports: [EmailJobService],
})
export class EmailModule {}
