import { Inject, Injectable } from '@nestjs/common';
import {
  AUTO_TOP_UP_EMAIL_QUEUE_JOB_NAME,
  EXTERNAL_DEALLOCATION_EMAIL_QUEUE_JOB_NAME,
  GPU_BACK_ONLINE_EMAIL_QUEUE_JOB_NAME,
  PAYMENT_TOP_UP_EMAIL_QUEUE_JOB_NAME,
  SearchGpuData,
  UN_PRICED_GPU_FOUND_EMAIL_QUEUE_JOB_NAME,
  OUTRAGE_REFUND_EMAIL_QUEUE_JOB_NAME,
  GPU_GONE_OFFLINE_EMAIL_QUEUE_JOB_NAME,
} from '@neural/models';
import { Queue as BullQueue, Job } from 'bull';
import { EMAIL_QUEUE_TOKEN } from './email-token';
import { EmailService } from './email.service';

@Injectable()
export class EmailProcessor {
  constructor(private readonly emailService: EmailService, @Inject(EMAIL_QUEUE_TOKEN) private emailQueue: BullQueue | null) {
    if (this.emailQueue) {
      this.emailQueue.process(AUTO_TOP_UP_EMAIL_QUEUE_JOB_NAME, this.sendAutoTopUpAuthorizationEmail.bind(this));
      this.emailQueue.process(EXTERNAL_DEALLOCATION_EMAIL_QUEUE_JOB_NAME, this.sendExternalDeallocationEmail.bind(this));
      this.emailQueue.process(UN_PRICED_GPU_FOUND_EMAIL_QUEUE_JOB_NAME, this.sendUnpricedGpuFoundEmail.bind(this));
      this.emailQueue.process(PAYMENT_TOP_UP_EMAIL_QUEUE_JOB_NAME, this.sendPaymentTopUpEmail.bind(this));
      this.emailQueue.process(GPU_BACK_ONLINE_EMAIL_QUEUE_JOB_NAME, this.sendGpuBackOnlineEmail.bind(this));
      this.emailQueue.process(OUTRAGE_REFUND_EMAIL_QUEUE_JOB_NAME, this.sendOutrageRefundEmail.bind(this));
      this.emailQueue.process(GPU_GONE_OFFLINE_EMAIL_QUEUE_JOB_NAME, this.sendGpuGoneOfflineEmail.bind(this));
    }
  }
  async sendAutoTopUpAuthorizationEmail(job: Job<{ userEmail: string; name: string; authorizationLink: string }>) {
    const { userEmail, name, authorizationLink } = job.data;
    return this.emailService.sendAutoTopUpAuthorizationEmail(userEmail, name, authorizationLink);
  }
  async sendExternalDeallocationEmail(
    job: Job<{ userEmail: string; userName: string; gpuName: string; gpuModel: string; deallocatedAt: Date }>,
  ) {
    const { userEmail, userName, gpuName, gpuModel, deallocatedAt: date } = job.data;
    return this.emailService.sendExternalDeallocationEmail(userEmail, userName, gpuName, gpuModel, date);
  }
  async sendUnpricedGpuFoundEmail(job: Job<{ userEmail: string; name: string; gpusAvailable: SearchGpuData[] }>) {
    const { userEmail, name, gpusAvailable } = job.data;
    return this.emailService.sendAvailableGpusEmail(userEmail, name, gpusAvailable);
  }
  async sendPaymentTopUpEmail(job: Job<{ userName: string; userEmail: string; balance: string; amount: string }>) {
    const { userName, userEmail, balance, amount } = job.data;
    return this.emailService.sendPaymentTopupNotification(userName, userEmail, balance, amount);
  }
  async sendGpuBackOnlineEmail(job: Job<{ userEmail: string; userName: string; gpuName: string; gpuModel: string }>) {
    const { userEmail, userName, gpuName, gpuModel } = job.data;
    return this.emailService.sendGpuBackOnlineEmail(userEmail, userName, gpuName, gpuModel);
  }

  async sendOutrageRefundEmail(
    job: Job<{
      userName: string;
      userEmail: string;
      refundAmount: string;
      outageStart: string;
      outageEnd: string;
      percentage: string;
    }>,
  ) {
    const { userName, userEmail, refundAmount, outageStart, outageEnd, percentage } = job.data;
    return this.emailService.sendOutrageRefundEmail(userName, userEmail, refundAmount, outageStart, outageEnd, percentage);
  }

  async sendGpuGoneOfflineEmail(job: Job<{ userEmail: string; userName: string; gpuName: string; gpuModel: string }>) {
    const { userEmail, userName, gpuName, gpuModel } = job.data;
    return this.emailService.sendGpuGoneOfflineEmail(userEmail, userName, gpuName, gpuModel);
  }
}
