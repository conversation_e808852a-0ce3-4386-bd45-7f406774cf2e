import * as fs from 'fs';
import * as path from 'path';
import { Injectable } from '@nestjs/common';
import { SearchGpuData } from '@neural/models';
import { format } from 'date-fns';
import * as handlebars from 'handlebars';
import { Attachment } from 'nodemailer/lib/mailer';
import { EmailTransportService } from './mail.service';

@Injectable()
export class EmailService {
  constructor(private emailTransportService: EmailTransportService) {}

  async sendAutoTopUpAuthorizationEmail(userEmail: string, name: string, authorizationLink: string) {
    const templatePath = path.join(__dirname, 'assets/email-templates/auto-top-up-authorization.html');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const template = handlebars.compile(templateHtml);

    const currentYear = new Date().getFullYear();

    const html = template({ name, authorizationLink, currentYear });
    const text = `Hi ${name},
                  We attempted to complete an auto top-up on your account, but your card requires authorization to proceed with the payment.

                  Please follow this link to complete the 3D Secure authentication and authorize your payment: ${authorizationLink}

                  If you didn’t initiate this payment, you can safely ignore this email.

                  Thank you for using our service!

                  Best regards,
                  Neural Internet

                  Contact: <EMAIL>
                  © ${currentYear} Your Company. All rights reserved.`;

    const attachments: Attachment[] = [
      {
        filename: 'logo.png',
        path: path.join(__dirname, 'assets/email-templates/logo-white.png'),
        cid: 'unique@neuralinternet',
      },
    ];

    return this.emailTransportService.sendEmail(userEmail, 'Auto Top-Up Authorization Required', text, html, attachments);
  }

  async sendAvailableGpusEmail(userEmail: string, name: string, gpusAvailable: SearchGpuData[]) {
    const templatePath = path.join(__dirname, 'assets/email-templates/available_gpus.html');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const template = handlebars.compile(templateHtml);

    const currentYear = new Date().getFullYear();
    const gpusNames = gpusAvailable.map((gpus) => gpus.gpu_name);
    const html = template({ name, currentYear, gpusAvailable, gpusNames: gpusNames });
    const gpus = gpusAvailable
      .map((gpu) => `Model: ${gpu.gpu_name}, Specifications:  ${gpu.gpu_capacity} GB VRAM,  ${gpu.ram} GB RAM, ${gpu.cpu_count}  vCPU`)
      .join('\n');
    const text = `Dear ${name},

                  This email is to inform you that the ${gpusNames} GPU has been detected and is available for use in your system. To enable users to deploy this GPU, please update the pricing for it in the Neural Internet dashboard.

                  The following are the specifications for the GPUs:

                  ${gpus}

                  Contact: <EMAIL>
                  © ${currentYear} Your Company. All rights reserved.`;

    const attachments: Attachment[] = [
      {
        filename: 'logo.png',
        path: path.join(__dirname, 'assets/email-templates/logo-white.png'),
        cid: 'unique@neuralinternet',
      },
    ];

    return this.emailTransportService.sendEmail(userEmail, `Update Pricing for ${gpusNames}`, text, html, attachments);
  }

  async sendPaymentTopupNotification(userName: string, userEmail: string, balance: string, amount: string) {
    const templatePath = path.join(__dirname, 'assets/email-templates/payment-top-up.html');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const template = handlebars.compile(templateHtml);

    const currentYear = new Date().getFullYear();

    const html = template({ userName, balance, amount, currentYear });
    const text = `
                Hi ${userName},

                Your account has been successfully topped up with ${amount}. Your new balance is ${balance}.
                Thank you for ensuring uninterrupted access to your GPU rentals. If you have any questions or need assistance, feel free to contact support.
                Happy computing!

                If you have any questions, please feel free to contact our support <NAME_EMAIL>.

                Thank you for using our services!

                © ${currentYear} Your Company. All rights reserved.
                If you have any questions, contact <NAME_EMAIL>`;

    const attachments: Attachment[] = [
      {
        filename: 'logo.png',
        path: path.join(__dirname, 'assets/email-templates/logo-white.png'),
        cid: 'unique@neuralinternet',
      },
    ];

    return this.emailTransportService.sendEmail(userEmail, 'Success: Balance Topped Up', text, html, attachments);
  }

  async sendExternalDeallocationEmail(userEmail: string, userName: string, gpuName: string, gpuModel: string, deallocatedAtIn: Date) {
    const templatePath = path.join(__dirname, 'assets/email-templates/external-deallocation.html');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const template = handlebars.compile(templateHtml);

    const currentYear = new Date().getFullYear();

    const deallocatedAt = format(deallocatedAtIn, 'MMMM dd, yyyy HH:mm:ss a');

    const html = template({ userName, gpuName, gpuModel, deallocatedAt, currentYear });

    const text = `Notice: GPU Deallocation

                  Hi ${userName},


                  Your ${gpuModel} GPU: ${gpuName} has been deallocated as of ${deallocatedAt}. This may happen for one or more of the following reasons:


                  - Your balance is insufficient to continue the rental.

                  - Maintenance or technical issues required the deallocation.


                  If you believe this was unexpected, please check your balance, review your rental settings, or contact support for assistance.

                  We apologize for any inconvenience and appreciate your understanding.

                  If you have any questions, please feel free to contact our support <NAME_EMAIL>.

                  Thank you for using our services!


                  © ${currentYear} Neural Internet. All rights reserved.
                  If you have any questions, contact <NAME_EMAIL>.`;

    const attachments: Attachment[] = [
      {
        filename: 'logo.png',
        path: path.join(__dirname, 'assets/email-templates/logo-white.png'),
        cid: 'unique@neuralinternet',
      },
    ];

    return this.emailTransportService.sendEmail(userEmail, 'GPU Deallocation Notification', text, html, attachments);
  }

  async sendGpuBackOnlineEmail(userEmail: string, userName: string, gpuName: string, gpuModel: string) {
    const templatePath = path.join(__dirname, 'assets/email-templates/gpu-back-online.html');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const template = handlebars.compile(templateHtml);
    const currentYear = new Date().getFullYear();

    const html = template({ userName, gpuName, gpuModel, currentYear });

    const text = `Update: GPU Back Online

                  Hi ${userName},


                  Good news! Your previously allocated ${gpuModel} GPU: ${gpuName} is now back online and available for use.
                  Please verify your workloads and ensure everything is running as expected.

                  If you have any questions, please feel free to contact our support <NAME_EMAIL>.

                  Thank you for using our services!


                  © ${currentYear} Neural Internet. All rights reserved.
                  If you have any questions, contact <NAME_EMAIL>.`;

    const attachments: Attachment[] = [
      {
        filename: 'logo.png',
        path: path.join(__dirname, 'assets/email-templates/logo-white.png'),
        cid: 'unique@neuralinternet',
      },
    ];

    return this.emailTransportService.sendEmail(userEmail, 'Update: GPU Back Online', text, html, attachments);
  }

  async sendOutrageRefundEmail(
    userName: string,
    userEmail: string,
    refundAmount: string,
    outageStart: string,
    outageEnd: string,
    percentage: string,
  ) {
    const templatePath = path.join(__dirname, 'assets/email-templates/outrage-refund-processed.html');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const template = handlebars.compile(templateHtml);
    const currentYear = new Date().getFullYear();

    const html = template({ userName, refundAmount, outageStart, outageEnd, percentage, currentYear });
    const text = `
                Hi ${userName},

                We have processed a refund of ${refundAmount} to your account. The amount has been successfully add your account balance. If you have any questions or need assistance, feel free to contact support.
                Happy computing!

                If you have any questions, please feel free to contact our support team at

                Thank you for using our services!

                © ${currentYear} Your Company. All rights reserved.
                If you have any questions, contact <NAME_EMAIL>`;

    const attachments: Attachment[] = [
      {
        filename: 'logo.png',
        path: path.join(__dirname, 'assets/email-templates/logo-white.png'),
        cid: 'unique@neuralinternet',
      },
    ];

    return this.emailTransportService.sendEmail(userEmail, 'Refund Processed', text, html, attachments);
  }

  async sendGpuGoneOfflineEmail(userEmail: string, userName: string, gpuName: string, gpuModel: string) {
    const templatePath = path.join(__dirname, 'assets/email-templates/gpu-gone-offline.html');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const template = handlebars.compile(templateHtml);
    const currentYear = new Date().getFullYear();

    const html = template({ userName, gpuName, gpuModel, currentYear });

    const text = `Update: GPU Offline

                  Hi ${userName},

                  We want to inform you that your allocated ${gpuModel} GPU: ${gpuName} has gone offline.

                  Our team is actively monitoring the situation, and we will update you as soon as the GPU is back online.

                  If you have any questions, please feel free to contact our support <NAME_EMAIL>.

                  Thank you for using our services!


                  © ${currentYear} Neural Internet. All rights reserved.
                  If you have any questions, contact <NAME_EMAIL>.`;

    const attachments: Attachment[] = [
      {
        filename: 'logo.png',
        path: path.join(__dirname, 'assets/email-templates/logo-white.png'),
        cid: 'unique@neuralinternet',
      },
    ];

    return this.emailTransportService.sendEmail(userEmail, 'Update: GPU Offline', text, html, attachments);
  }
}
