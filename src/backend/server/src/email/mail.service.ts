import { Inject, Logger, Injectable } from '@nestjs/common';
import { ISMTPConfig } from '@neural/models';
import * as nodemailer from 'nodemailer';
import { Attachment, Options } from 'nodemailer/lib/mailer';
import { SMTP_CONFIG_TOKEN } from './email-token';

@Injectable()
export class EmailTransportService {
  private transporter: nodemailer.Transporter;

  constructor(@Inject(SMTP_CONFIG_TOKEN) private options: ISMTPConfig | null, private readonly logger: Logger) {
    if (options) {
      this.transporter = nodemailer.createTransport({
        host: options.host,
        port: options.port,
        secure: <PERSON><PERSON><PERSON>(options.secure),
        auth: options.auth,
      });
    } else {
      this.logger.warn('SMTP configuration is missing. Emails will not be sent.');
    }
  }

  async sendEmail(to: string, subject: string, text: string, html: string, attachments?: Attachment[]) {
    try {
      if (!this.transporter || !this.options) {
        this.logger.warn('SMTP configuration is missing. Emails will not be sent.');
        return;
      }

      const mailOptions: Options = {
        from: this.options.from,
        to,
        subject,
        text,
        html,
        attachments: attachments || [],
      };
      await this.transporter.sendMail(mailOptions);
    } catch (error) {
      this.logger.error(`Failed to send email. Error: ${error.message}`);
    }
  }
}
