import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GpusRented } from '../../../gpu/entities/gpus-rented.entity';
import { User } from '../../../user/entities/user.entity';

@Injectable()
export class GpuUtilityService {
  private readonly logger = new Logger(GpuUtilityService.name);
  constructor(
    @InjectRepository(GpusRented)
    private readonly gpusRentedRepository: Repository<GpusRented>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * How long (in hours) the GPU rental can continue based on the remaining balance
   * @param userId The ID of the user for whom to calculate the current runway time.
   * @returns The remaining rental time in hours as a decimal value.
   *          Returns 0 if the user has no balance or if no GPUs are allocated.
   *
   */
  async calculateCurrentRunwayTime(userId: string): Promise<number> {
    const Gpus = await this.gpusRentedRepository.find({
      where: { is_allocated: true, user_id: userId },
    });
    const totalGpusHourlyCost = Gpus.map((gpuRented) => gpuRented.price).reduce((sum, price) => sum + price, 0);

    const user = await this.userRepository.findOne({ where: { user_id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (totalGpusHourlyCost === 0) return 0;
    return user.balance / (totalGpusHourlyCost * 100);
  }
}
