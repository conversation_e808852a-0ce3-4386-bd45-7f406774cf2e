import { ApiProperty } from '@nestjs/swagger';
import { IDeallocationEventDto } from '@neural/models';
import { Type } from 'class-transformer';
import { IsString, IsUUID, IsDate } from 'class-validator';

export class DeallocationEventDto implements IDeallocationEventDto {
  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  time: Date;

  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  deallocated_at: Date;

  @ApiProperty()
  @IsString()
  hotkey: string;

  @ApiProperty()
  @IsString()
  status: string;

  @ApiProperty()
  @IsUUID()
  uuid: string;
}
