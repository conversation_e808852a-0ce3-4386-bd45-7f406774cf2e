import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IDeployGpuDto } from '@neural/models';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class DeployGpuDto implements IDeployGpuDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  hotkey: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional()
  @IsUUID()
  @IsOptional()
  ssh_key_id?: string;
}
