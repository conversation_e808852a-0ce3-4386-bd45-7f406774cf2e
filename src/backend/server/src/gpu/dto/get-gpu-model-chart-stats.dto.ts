import { ApiProperty } from '@nestjs/swagger';
import { IGetGpuModelChartStatsDto, IPeriod } from '@neural/models';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber } from 'class-validator';

export class GetGpuModelChartStatsDto implements IGetGpuModelChartStatsDto {
  @ApiProperty({ description: 'Period of the earning data', enum: IPeriod, enumName: 'IPeriod' })
  @IsEnum(IPeriod)
  period: IPeriod;

  @ApiProperty({
    description: 'Gpu Model Id',
    type: Number,
  })
  @Type(() => Number)
  @IsNumber()
  gpuModelId: number;
}
