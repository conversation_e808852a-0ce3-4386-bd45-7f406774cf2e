import { ApiPropertyOptional } from '@nestjs/swagger';
import { IGpuPricesSearch } from '@neural/models';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsNumber } from 'class-validator';
import { IsStringSanitized } from '../../shared';

export class GetGpuPricesDto implements IGpuPricesSearch {
  @ApiPropertyOptional({ type: String, description: 'Name of the GPU' })
  @IsOptional()
  @Transform(({ value }) => (typeof value === 'string' ? value.trim() : value))
  @IsStringSanitized()
  gpu_name?: string;

  @ApiPropertyOptional({ type: Number, description: 'Minimum GPU capacity' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gpu_capacity_min?: number;

  @ApiPropertyOptional({ type: Number, description: 'Maximum GPU capacity' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gpu_capacity_max?: number;

  @ApiPropertyOptional({ type: Number, description: 'Minimum price' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  price_range_min?: number;

  @ApiPropertyOptional({ type: Number, description: 'Maximum price' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  price_range_max?: number;

  @ApiPropertyOptional({ type: String, description: 'Sort by column' })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({ type: String, description: 'Sort direction' })
  @IsOptional()
  @IsString()
  sortDirection?: string;

  @ApiPropertyOptional({ type: Number, description: 'Number of records to skip', default: 0 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  skip?: number = 0;

  @ApiPropertyOptional({ type: Number, description: 'Number of records to take', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  take?: number = 10;
}
