import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsNumber, Min } from 'class-validator';

export class GetGpuResourcesDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    type: Number,
    minimum: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  @Min(1)
  page_number?: number;

  @ApiPropertyOptional({
    description: 'Page size for pagination',
    type: Number,
    minimum: 1,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  @Min(1)
  page_size?: number;
}
