import { ApiPropertyOptional } from '@nestjs/swagger';
import { IRentedGpuSearch, IRentedGpuSortByEnum, IRentedGpuSortDirectionEnum } from '@neural/models';
import { Type } from 'class-transformer';
import { IsOptional, IsNumber, IsEnum } from 'class-validator';
import { IsStringSanitized } from '../../shared/decorators/is-sanitized.decorator';

export class ListDeployedGpuResourceDto implements IRentedGpuSearch {
  @ApiPropertyOptional({ description: 'Name of the resource', type: String })
  @IsOptional()
  @IsStringSanitized()
  name?: string;

  @ApiPropertyOptional({ description: 'Minimum CPU count', type: Number })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  cpu_count_min?: number;

  @ApiPropertyOptional({ description: 'Maximum CPU count', type: Number })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  cpu_count_max?: number;

  @ApiPropertyOptional({ description: 'Minimum GPU capacity', type: Number })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  gpu_capacity_min?: number;

  @ApiPropertyOptional({ description: 'Maximum GPU capacity', type: Number })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  gpu_capacity_max?: number;

  @ApiPropertyOptional({ description: 'Minimum hard disk total', type: Number })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  hard_disk_total_min?: number;

  @ApiPropertyOptional({ description: 'Maximum hard disk total', type: Number })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  hard_disk_total_max?: number;

  @ApiPropertyOptional({ description: 'Minimum RAM total', type: Number })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  ram_total_min?: number;

  @ApiPropertyOptional({ description: 'Maximum RAM total', type: Number })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  ram_total_max?: number;

  @ApiPropertyOptional({ type: Number, description: 'Minimum price' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  price_range_min?: number;

  @ApiPropertyOptional({ type: Number, description: 'Maximum price' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  price_range_max?: number;

  @ApiPropertyOptional({ description: 'Number of records to skip', type: Number, default: 0 })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  skip?: number = 0;

  @ApiPropertyOptional({ description: 'Number of records to take', type: Number, default: 10 })
  @IsOptional()
  @Type(() => Number) // Ensure the value is transformed to a number
  @IsNumber()
  take?: number = 10;

  @ApiPropertyOptional({ description: 'Sort by column', enum: IRentedGpuSortByEnum, enumName: 'IRentedGpuSortByEnum' })
  @IsOptional()
  @IsEnum(IRentedGpuSortByEnum)
  sortBy?: IRentedGpuSortByEnum;

  @ApiPropertyOptional({ description: 'Sort direction', enum: IRentedGpuSortDirectionEnum, enumName: 'IRentedGpuSortDirectionEnum' })
  @IsOptional()
  @IsEnum(IRentedGpuSortDirectionEnum)
  sortDirection?: IRentedGpuSortDirectionEnum;
}
