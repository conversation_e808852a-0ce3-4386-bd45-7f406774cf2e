import { ApiProperty } from '@nestjs/swagger';
import { IOfflineWarningEventDto } from '@neural/models';
import { Type } from 'class-transformer';
import { IsString, IsUUID, IsDate } from 'class-validator';

export class OfflineWarningEventDto implements IOfflineWarningEventDto {
  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  time: Date;

  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  status_change_at: Date;

  @ApiProperty()
  @IsString()
  hotkey: string;

  @ApiProperty()
  @IsString()
  status: string;

  @ApiProperty()
  @IsUUID()
  uuid: string;
}
