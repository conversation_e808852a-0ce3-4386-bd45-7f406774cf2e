import { ApiPropertyOptional } from '@nestjs/swagger';
import { IGpuResourceSearch } from '@neural/models';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsNumber } from 'class-validator';
import { IsStringSanitized } from '../../shared/decorators/is-sanitized.decorator';

export class SearchGpuResourceDto implements IGpuResourceSearch {
  @ApiPropertyOptional({ type: String, description: 'Name of the GPU' })
  @IsOptional()
  @Transform(({ value }) => (typeof value === 'string' ? value.trim() : value))
  @IsStringSanitized()
  gpu_name?: string;

  @ApiPropertyOptional({ type: Number, description: 'Minimum CPU count' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cpu_count_min?: number;

  @ApiPropertyOptional({ type: Number, description: 'Maximum CPU count' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cpu_count_max?: number;

  @ApiPropertyOptional({ type: Number, description: 'Minimum GPU capacity' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gpu_capacity_min?: number;

  @ApiPropertyOptional({ type: Number, description: 'Maximum GPU capacity' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gpu_capacity_max?: number;

  @ApiPropertyOptional({ type: Number, description: 'Minimum total hard disk space' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  hard_disk_total_min?: number;

  @ApiPropertyOptional({ type: Number, description: 'Maximum total hard disk space' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  hard_disk_total_max?: number;

  @ApiPropertyOptional({ type: Number, description: 'Minimum total RAM' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ram_total_min?: number;

  @ApiPropertyOptional({ type: Number, description: 'Maximum total RAM' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ram_total_max?: number;

  @ApiPropertyOptional({ type: Number, description: 'Minimum price' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  price_range_min?: number;

  @ApiPropertyOptional({ type: Number, description: 'Maximum price' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  price_range_max?: number;

  @ApiPropertyOptional({ type: Number, description: 'Page number' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page_number?: number;

  @ApiPropertyOptional({ type: Number, description: 'Page size' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page_size?: number;
}
