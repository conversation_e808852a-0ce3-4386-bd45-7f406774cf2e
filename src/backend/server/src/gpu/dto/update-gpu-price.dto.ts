import { ApiPropertyOptional } from '@nestjs/swagger';
import { IUpdateGpuPriceDto } from '@neural/models';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';

export class UpdateGpuPricesDto implements IUpdateGpuPriceDto {
  @ApiPropertyOptional({ required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  vram?: number;

  @ApiPropertyOptional({ required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  price?: number;
}
