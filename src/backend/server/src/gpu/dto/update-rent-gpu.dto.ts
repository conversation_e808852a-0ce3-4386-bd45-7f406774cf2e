import { ApiPropertyOptional } from '@nestjs/swagger';
import { IUpdateRentedGpuDto } from '@neural/models';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateRentedGpuDto implements IUpdateRentedGpuDto {
  @ApiPropertyOptional({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ required: false })
  @IsOptional()
  @IsUUID()
  ssh_key_id?: string;
}
