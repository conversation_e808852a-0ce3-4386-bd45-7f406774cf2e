import { IGpuCostHistory } from '@neural/models';
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, DeleteDateColumn } from 'typeorm';
import { User } from '../../user/entities/user.entity';

@Entity('gpus_cost_history')
export class GpuCostHistory implements IGpuCostHistory {
  @PrimaryGeneratedColumn('uuid')
  gpu_cost_history_id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @ManyToOne(() => User, (user) => user.gpu_cost_history, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'date' })
  date_cost_history: Date;

  @Column({ type: 'float', nullable: true })
  total_hours: number;

  @Column({ type: 'float' })
  cost: number;

  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deleted_at: Date | null;
}
