import { IGpuDailyCount } from '@neural/models';
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('gpus_daily_count')
export class GpuDailyCount implements IGpuDailyCount {
  @PrimaryGeneratedColumn('uuid')
  daily_gpu_count_id: string;

  @Column({ type: 'timestamptz' })
  date: Date;

  @Column({ type: 'int' })
  total_available_gpu: number;

  @Column({ type: 'int' })
  total_rented_gpu: number;
}
