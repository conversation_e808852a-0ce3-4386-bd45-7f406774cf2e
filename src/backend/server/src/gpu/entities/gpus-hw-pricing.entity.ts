import { IGpusHowPricing } from '@neural/models';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('gpus_hw_pricing')
export class GpuHwPricing implements IGpusHowPricing {
  @PrimaryGeneratedColumn('uuid')
  gpu_hw_pricing_id: string;

  @Column({ type: 'float4' })
  ram: number;

  @Column({ type: 'float4' })
  cpu: number;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;
}
