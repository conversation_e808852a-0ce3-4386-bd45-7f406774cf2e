import { IGpusPricing } from '@neural/models';
import { Column, CreateDateColumn, Entity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { GpusRented } from './gpus-rented.entity';

@Entity('gpus_pricing')
export class GpusPricing implements IGpusPricing {
  @PrimaryGeneratedColumn('uuid')
  gpu_pricing_id: string;

  @Column({ type: 'float4', nullable: true })
  price: number;

  @Column({ type: 'varchar' })
  model: string;

  @Column({ type: 'int2' })
  vram: number;

  @OneToMany(() => GpusRented, (gpusRented) => gpusRented.gpusPrice)
  gpus_rented: GpusRented[];

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;
}
