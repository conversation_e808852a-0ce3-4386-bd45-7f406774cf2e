import { GpuDeallocateByEnum, IGpusRented } from '@neural/models';
import {
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  DeleteDateColumn,
  OneToMany,
} from 'typeorm';
import { Payment } from '../../payment/entities/payment.entity';
import { SshKey } from '../../user/entities/ssh-key.entity';
import { User } from '../../user/entities/user.entity';
import { GpusPricing } from './gpus-pricing.entity';
import { GpusModels } from './gpus_models.entity';

@Entity('gpus_rented')
export class GpusRented implements IGpusRented {
  @PrimaryGeneratedColumn('uuid')
  gpu_rented_id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ type: 'varchar', length: 128 })
  name: string;

  @Column({ type: 'varchar' })
  model: string;

  @Column({ type: 'varchar' })
  hotkey: string;

  @Column({ type: 'uuid' })
  gpu_pricing_id: string;

  @Column({ type: 'float' })
  price: number;

  @Column({ type: 'uuid', nullable: true })
  allocation_uuid?: string;

  @Column({ type: 'varchar' })
  status: string;

  @Column({ type: 'varchar' })
  public_ip: string;

  @Column({ type: 'integer' })
  port: number;

  @Column({ type: 'enum', enum: GpuDeallocateByEnum, default: GpuDeallocateByEnum.USER })
  deallocated_by: GpuDeallocateByEnum;

  @Column({ type: 'boolean', default: true })
  is_allocated: boolean;

  @Column({ type: 'float', nullable: true })
  ram: number;

  @Column({ type: 'integer', nullable: true })
  cpu_count: number;

  @Column({ type: 'float', nullable: true })
  gpu_capacity: number;

  @Column({ type: 'integer', nullable: true })
  gpu_count: number;

  @Column({ type: 'float', nullable: true })
  hard_disk: number;

  @Column({ type: 'boolean', nullable: true, default: null })
  deallocatable_on_api: boolean | null;

  @ManyToOne(() => User, (user) => user.gpus_rented, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => GpusPricing, (gpusPrice) => gpusPrice.gpus_rented)
  @JoinColumn({ name: 'gpu_pricing_id' })
  gpusPrice: GpusPricing | undefined;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deleted_at: Date | null;

  @Column({ type: 'timestamptz', nullable: true })
  last_payment_date: Date | null;

  @Column({ type: 'timestamptz', nullable: true })
  deallocated_at: Date;

  @Column({ type: 'float', default: 0 })
  total_cost: number;

  @Column({ type: 'boolean', default: false })
  is_payment_suspended: boolean;

  //TODO: Remove this column after the migration
  @Column({ type: 'boolean', default: false })
  is_offline_status_notified_to_user: boolean;

  @Column({ type: 'timestamptz', nullable: true })
  offline_status_received_at: Date | null;

  @Column({ type: 'timestamptz', nullable: true })
  online_status_received_at: Date | null;

  //TODO: Remove this column after the migration
  @Column({ type: 'boolean', default: false })
  is_payment_in_grace_period: boolean;

  @Column({ type: 'float', default: 0 })
  total_hours: number;

  @Column({ type: 'integer' })
  gpu_model_id: number;

  @Column({ type: 'varchar' })
  gpu_model: string;

  @Column({ type: 'int2', nullable: true })
  allocation_fault_count: number | null; //0 on the first try, 1 on the second try, 2 on the third try

  @Column({ type: 'integer', nullable: true })
  miner_version: number | null;

  @ManyToOne(() => GpusModels, (gpusModels) => gpusModels.gpus_model_id)
  @JoinColumn({ name: 'gpu_model_id' })
  gpuModel: GpusModels;

  @OneToMany(() => Payment, (payment) => payment.gpuRented)
  payments: Payment[];

  @Column({ type: 'uuid', nullable: true })
  ssh_key_id: string | null;

  @ManyToOne(() => SshKey, (sshkeyModels) => sshkeyModels.ssh_key_id)
  @JoinColumn({ name: 'ssh_key_id' })
  sshkeyModel: SshKey;
}
