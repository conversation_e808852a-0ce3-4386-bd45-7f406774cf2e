import { IGpusUsageSessions } from '@neural/models';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('gpus_usage_sessions')
export class GpusUsageSessions implements IGpusUsageSessions {
  @PrimaryGeneratedColumn('increment')
  gpu_usage_session_id: number;

  @Column({ type: 'uuid', nullable: false })
  gpu_rented_id: string;

  @Column({ type: 'smallint', nullable: false })
  state: number;

  @Column({ type: 'timestamptz', nullable: false })
  start_date: Date;

  @Column({ type: 'timestamptz', nullable: true })
  end_date: Date | null;

  @Column({ type: 'float', nullable: false })
  cost: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
