import { IGpuAllocationStats } from '@neural/models';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('gpus_allocation_stats')
export class GpuAllocationStats implements IGpuAllocationStats {
  @PrimaryGeneratedColumn('increment')
  gpus_allocation_stats_id: number;

  @Column({ type: 'varchar', unique: true })
  hotkey: string;

  @Column({ type: 'int4' })
  total_allocation_count: number;

  @Column({ type: 'float8' })
  longest_rental_hours: number;

  @Column({ type: 'float4' })
  reliability: number;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz', nullable: true })
  updated_at: Date;
}
