import { IGpusCharts } from '@neural/models';
import { Col<PERSON>n, CreateDateColumn, UpdateDateColumn, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Entity } from 'typeorm';
import { GpusModels } from './gpus_models.entity';

@Entity('gpus_charts')
export class GpusCharts implements IGpusCharts {
  @PrimaryGeneratedColumn('increment')
  gpus_chart_id: number;

  @Column({ type: 'date', unique: true })
  date: Date;

  @Column({ type: 'integer' })
  gpu_model_id: number;

  @Column({ type: 'integer', default: 0 })
  available: number;

  @Column({ type: 'integer', default: 0 })
  hours: number;

  @Column({ type: 'integer', default: 0 })
  rented: number;

  @Column({ type: 'float', default: 0 })
  earned: number;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  @ManyToOne(() => GpusModels, (gpusModels) => gpusModels.gpus_model_id)
  @JoinColumn({ name: 'gpu_model_id' })
  gpusModel: GpusModels;
}
