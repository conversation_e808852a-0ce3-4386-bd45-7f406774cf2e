import { IGpuDeploymentFailure } from '@neural/models';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('gpus_deployment_failure')
export class GpuDeploymentFailure implements IGpuDeploymentFailure {
  @PrimaryGeneratedColumn('increment')
  gpus_deployment_failure_id: number;

  @Column({ type: 'varchar', unique: true })
  hotkey: string;

  @Column({ type: 'int2', nullable: true })
  status_code: number | null;

  @Column({ type: 'timestamptz' })
  deployment_time: Date;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz', nullable: true })
  updated_at: Date;
}
