import { IGpusModels } from '@neural/models';
import {
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  PrimaryGeneratedColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { GpusPricing } from './gpus-pricing.entity';

@Entity('gpus_models')
export class GpusModels implements IGpusModels {
  @PrimaryGeneratedColumn()
  gpus_model_id: number;

  @Column({ type: 'varchar' }) //gpu_name
  gpu_model: string;

  @Column({ type: 'integer' }) //ram
  ram: number;

  @Column({ type: 'integer' }) //cpu_count
  cpu: number;

  @Column({ type: 'uuid' })
  gpu_pricing_id: string;

  @ManyToOne(() => GpusPricing, (gpusPrice) => gpusPrice.gpus_rented)
  @JoinColumn({ name: 'gpu_pricing_id' })
  gpuPricing: GpusPricing | undefined;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deleted_at: Date | null;
}
