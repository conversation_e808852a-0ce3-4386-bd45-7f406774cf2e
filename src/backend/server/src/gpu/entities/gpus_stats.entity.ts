import { IGpuChartStats } from '@neural/models';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('gpus_stats')
export class GpuStats implements IGpuChartStats {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'bigint' })
  gpu_model_id: number;

  @Column({ type: 'float4', nullable: true })
  retention_rate: number;

  @Column({ type: 'float4', nullable: true })
  duration_7d: number;

  @Column({ type: 'float4', nullable: true })
  duration_30d: number;

  @Column({ type: 'float4', nullable: true })
  duration_90d: number;

  @Column({ type: 'float4', nullable: true })
  duration_1y: number;

  @Column({ type: 'float4', nullable: true })
  earnings_7d: number;

  @Column({ type: 'float4', nullable: true })
  earnings_30d: number;

  @Column({ type: 'float4', nullable: true })
  earnings_90d: number;

  @Column({ type: 'float4', nullable: true })
  earnings_1y: number;

  @Column({ type: 'float4', nullable: true })
  util_7d: number;

  @Column({ type: 'float4', nullable: true })
  util_30d: number;

  @Column({ type: 'float4', nullable: true })
  util_90d: number;

  @Column({ type: 'float4', nullable: true })
  util_1y: number;

  @Column({ type: 'float4', nullable: true })
  avg_7d: number;

  @Column({ type: 'float4', nullable: true })
  avg_30d: number;

  @Column({ type: 'float4', nullable: true })
  avg_90d: number;

  @Column({ type: 'float4', nullable: true })
  avg_1y: number;

  @Column({ type: 'float4', nullable: true })
  median_7d: number;

  @Column({ type: 'float4', nullable: true })
  median_30d: number;

  @Column({ type: 'float4', nullable: true })
  median_90d: number;

  @Column({ type: 'float4', nullable: true })
  median_1y: number;

  @Column({ type: 'timestamptz', nullable: true })
  rentals_7d: number;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp', nullable: true })
  updated_at: Date;
}
