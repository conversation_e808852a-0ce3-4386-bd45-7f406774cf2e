import { Body, Controller, Get, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { IGetGpuModelChartStatsDto } from '@neural/models';
import type { Request } from 'express';
import { AdminGuard, AuthGuard } from '../auth/guards';
import { SupabaseService } from '../core/supabase/supabase.service';
import {
  DeallocateGpuDto,
  DeployGpuDto,
  GetGpuResourceByHotkeyDto,
  GetMinerVersionDto,
  GetUserHistoricalCostsDto,
  GpuRentedIdDto,
  ListDeployedGpuResourceDto,
  RestartGpuDto,
  SearchGpuResourceDto,
  UpdateRentedGpuDto,
} from './dto';
import { GetGpuPricesDto } from './dto/get-gpu-prices.dto';
import { GpuPriceIdDto } from './dto/gpu-price-id.dto';
import { UpdateGpuPricesDto } from './dto/update-gpu-price.dto';
import { GpuChartsService } from './service/gpu-charts.service';
import { GpuService } from './service/gpu.service';

@ApiTags('gpus')
@Controller('gpus')
export class GpuController {
  constructor(
    private readonly gpuService: GpuService,
    private readonly supabaseService: SupabaseService,
    private readonly gpuChartsService: GpuChartsService,
  ) {}

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get()
  async getAvailableGpuResource(@Query() body: SearchGpuResourceDto) {
    return await this.gpuService.getAvailableGpuResource(body);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('deploy')
  async deployGpuResource(@Req() request: Request, @Body() deployGpuDto: DeployGpuDto) {
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    return await this.gpuService.deployGpuResource(user_id, deployGpuDto);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post(':gpu_rented_id/deallocate')
  async deallocateGpuResource(@Req() request: Request, @Param() deallocateGpuDto: DeallocateGpuDto) {
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    const { gpu_rented_id } = deallocateGpuDto;
    return await this.gpuService.deallocateGpuResource(user_id, gpu_rented_id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('rented/list')
  async listDeployedGpuResource(@Req() request: Request, @Query() query: ListDeployedGpuResourceDto) {
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    return await this.gpuService.listDeployedGpuResource(user_id, query);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get(':hotkey')
  async getGpuResourceByHotkey(@Param() params: GetGpuResourceByHotkeyDto) {
    const { hotkey } = params;
    return await this.gpuService.getGpuResourceByHotkey(hotkey);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Put(':gpu_rented_id')
  async updateRentedGpu(@Req() request: Request, @Param() params: GpuRentedIdDto, @Body() updateRentedGpuDto: UpdateRentedGpuDto) {
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    const { gpu_rented_id } = params;
    return await this.gpuService.editRentedGpu(user_id, gpu_rented_id, updateRentedGpuDto);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('costs/data')
  async getUserHistoricalCosts(@Req() request: Request, @Query() query: GetUserHistoricalCostsDto) {
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    return this.gpuService.getUserHistoricalCosts(user_id, query.period);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('costs/average')
  async getAverageCost(@Req() request: Request) {
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    return this.gpuService.getAverageCost(user_id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('aggregated/resource-usage')
  async getResourceUsageForUser(@Req() request: Request) {
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    return this.gpuService.getResourceUsageForUser(user_id);
  }

  @ApiBearerAuth()
  @UseGuards(AdminGuard)
  @Get('total-rented-gpus/chart-data')
  async getTotalRentedGpusChartData(@Query() query: GetUserHistoricalCostsDto) {
    return this.gpuChartsService.getTotalRentedGpuChartData(query);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post(':gpu_rented_id/restart')
  async restartGpuResource(@Req() request: Request, @Param() params: RestartGpuDto) {
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    const { gpu_rented_id } = params;
    return this.gpuService.restartGpuResource(user_id, gpu_rented_id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('charts/top-three')
  async getTopThreeGpuChartsWithSeries(@Query() query: GetUserHistoricalCostsDto) {
    return this.gpuChartsService.getTopThreeGpuChartsWithSeries(query);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('charts/other')
  async getOtherRentedGpuChartsWithSeries(@Query() query: GetUserHistoricalCostsDto) {
    return this.gpuChartsService.getOtherGpusChartData(query);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('charts/comparative-utilization')
  async getComparativeUtilizationStats() {
    return this.gpuChartsService.getComparativeUtilizationStats();
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('models/charts/stats')
  async getGpuModelChartStats(@Query() query: IGetGpuModelChartStatsDto) {
    return this.gpuChartsService.getGpuModelChartStats(query);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get(':gpu_rented_id/details')
  async getGpuById(@Req() request: Request, @Param() params: GpuRentedIdDto) {
    const { gpu_rented_id } = params;
    const token = request.headers.authorization!;
    const { user_id } = await this.supabaseService.verifyToken(token);
    return await this.gpuService.getRentedGpuById(user_id, gpu_rented_id);
  }
  @ApiBearerAuth()
  @UseGuards(AdminGuard)
  @Get('prices/list')
  async ListPricesGpus(@Query() query: GetGpuPricesDto) {
    return await this.gpuService.ListPricesGpus(query);
  }

  @ApiBearerAuth()
  @UseGuards(AdminGuard)
  @Put('price/:gpu_pricing_id')
  async updatePriceGpu(@Param() params: GpuPriceIdDto, @Body() updatePriceGpuDto: UpdateGpuPricesDto) {
    const { gpu_pricing_id } = params;
    return await this.gpuService.updatePricesGpus(gpu_pricing_id, updatePriceGpuDto);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get(':hotkey/miner-version')
  async getMinerVersion(@Param() params: GetMinerVersionDto) {
    const { hotkey } = params;
    return await this.gpuService.getMinerVersion(hotkey);
  }
}
