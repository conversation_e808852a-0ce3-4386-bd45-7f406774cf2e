import https from 'https';
import { HttpModule } from '@nestjs/axios';
import { CacheModule } from '@nestjs/cache-manager';
import { forwardRef, Logger, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IDefaultGpusHowPricing, IBalanceMonitorConfig } from '@neural/models';
import { AxiosLoggingInterceptor } from '../core/interceptors/axios-logging.service';
import { SupabaseModule } from '../core/supabase/supabase.module';
import { EarningModule } from '../earning/earning.module';
import { EmailModule } from '../email/email.module';
import { GpuUtilityModule } from '../gpu-utility/gpu-utility.module';
import { InstanceIdModule } from '../instance-id';
import { LeaderElectionModule } from '../leader-election';
import { NotificationModule } from '../notification/notification.module';
import { PaymentModule } from '../payment/payment.module';
import { RedisModule } from '../redis';
import { ReportingModule } from '../reporting/reporting.module';
import { UserModule } from '../user';
import { SshKey } from '../user/entities/ssh-key.entity';
import { User } from '../user/entities/user.entity';
import {
  GpuAllocationStats,
  GpuCostHistory,
  GpuDailyCount,
  GpuDeploymentFailure,
  GpuHwPricing,
  GpusCharts,
  GpusModels,
  GpusPricing,
  GpusRented,
} from './entities';
import { GpusUsageSessions } from './entities/gpus-usage-sessions.entity';
import { GpuStats } from './entities/gpus_stats.entity';
import { GpuController } from './gpu.controller';
import { DEFAULT_GPUS_HW_PRICING, BALANCE_MONITOR_CONFIG } from './gpu.token';
import { GpuWebhook } from './hooks/gpu.webhook';
import { GpuHttpService } from './http/gpu.http.service';
import { AllocationCheckerService } from './jobs/allocation-checker-job.service';
import { BalanceMonitorService } from './jobs/balance-monitor-job.service';
import { DailyGpuCounterService } from './jobs/daily-gpu-counter-job.service';
import { GpuStatsService } from './jobs/gpu-stats.service';
import { GpuAllocationStatsService } from './jobs/gpus_allocation_stats.service';
import { MinerVersionUpdateJobService } from './jobs/miner-version-update-job.service';
import { CostService } from './service/cost.service';
import { GpuChartsService } from './service/gpu-charts.service';
import { GpuCostMonitorService } from './service/gpu-cost-monitor.service';
import { GpuUsageSessionsService } from './service/gpu-usage-sessions.service';
import { GpuService } from './service/gpu.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GpuCostHistory,
      GpuHwPricing,
      GpusPricing,
      GpusRented,
      GpuDailyCount,
      GpusModels,
      SshKey,
      User,
      GpusCharts,
      GpuStats,
      GpusUsageSessions,
      GpuAllocationStats,
      GpuDeploymentFailure,
    ]),
    HttpModule.registerAsync({
      imports: [ConfigModule],

      useFactory: async (configService: ConfigService) => {
        const gpuApiBaseUrl = configService.get<string>('gpu.url');
        const gpuApiClientKey = configService.get<string>('gpu.gpuApiClientKey');
        const gpuApiClientCert = configService.get<string>('gpu.gpuApiClientCert');
        const gpuApiCaCert = configService.get<string>('gpu.gpuApiCaCert');

        if (!gpuApiBaseUrl) {
          throw new Error(`Provide GPU API Base URL`);
        }

        if (!gpuApiClientKey) {
          throw new Error(`Provide GPU API Client Key`);
        }

        if (!gpuApiClientCert) {
          throw new Error(`Provide GPU API Client Cert`);
        }

        if (!gpuApiCaCert) {
          throw new Error(`Provide GPU API CA Cert`);
        }

        return {
          baseURL: gpuApiBaseUrl,
          headers: {
            accept: 'application/json',
          },
          httpsAgent: new https.Agent({
            rejectUnauthorized: false,
            requestCert: true,
            key: gpuApiClientKey,
            cert: gpuApiClientCert,
            ca: gpuApiCaCert,
          }),
        };
      },
      inject: [ConfigService],
    }),
    CacheModule.register({
      ttl: 5 * 60 * 1000, // 5 minutes
    }),
    SupabaseModule,
    PaymentModule,
    NotificationModule,
    EarningModule,
    forwardRef(() => UserModule),
    ReportingModule,
    GpuUtilityModule,
    EmailModule,
    InstanceIdModule,
    LeaderElectionModule,
    RedisModule,
  ],
  controllers: [GpuController, GpuWebhook],
  providers: [
    {
      provide: DEFAULT_GPUS_HW_PRICING,
      inject: [ConfigService],
      useFactory: async (configService: ConfigService): Promise<IDefaultGpusHowPricing> => {
        const pricePerRum = configService.get<number>('gpu.pricePerRum');
        const pricePerCpu = configService.get<number>('gpu.pricePerCpu');

        if (!pricePerRum || !pricePerCpu) {
          throw new Error(`Provide Default GPU's How Pricing Options`);
        }

        return {
          ram: pricePerRum,
          cpu: pricePerCpu,
        };
      },
    },
    {
      provide: BALANCE_MONITOR_CONFIG,
      inject: [ConfigService],
      useFactory: (configService: ConfigService): IBalanceMonitorConfig => {
        const balanceMonitorIntervalInMinutes = Number(configService.get<string>('gpu.balanceMonitorIntervalInMinutes'));
        const balanceBufferTimeInMinutes = Number(configService.get<string>('gpu.balanceBufferTimeInMinutes'));

        if (isNaN(balanceMonitorIntervalInMinutes)) {
          throw new Error('Invalid Balance Monitor Interval In Minutes');
        }

        if (!Number.isInteger(balanceMonitorIntervalInMinutes)) {
          throw new Error('Balance Monitor Interval In Minutes must be an integer.');
        }

        if (balanceMonitorIntervalInMinutes < 1 || balanceMonitorIntervalInMinutes > 60) {
          throw new Error('Balance Monitor Interval In Minutes must be between 1 and 60.');
        }

        if (isNaN(balanceBufferTimeInMinutes)) {
          throw new Error('Invalid Balance Buffer Time In Minutes');
        }

        if (balanceBufferTimeInMinutes < 1) {
          throw new Error('Balance Buffer Time In Minutes must be greater than 0.');
        }

        return {
          balanceMonitorIntervalInMinutes,
          balanceBufferTimeInMinutes,
        };
      },
    },

    GpuService,
    GpuHttpService,
    GpuCostMonitorService,
    BalanceMonitorService,
    DailyGpuCounterService,
    CostService,
    AllocationCheckerService,
    GpuChartsService,
    GpuStatsService,
    Logger,
    AxiosLoggingInterceptor,
    GpuUsageSessionsService,
    MinerVersionUpdateJobService,
    GpuAllocationStatsService,
  ],
  exports: [GpuService, GpuHttpService],
})
export class GpuModule {}
