import { Body, Controller, Post, Logger } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { IGpuPaymentStatus, IGpuWebhookResponse, NotificationType, OFFLINE, ONLINE } from '@neural/models';
import { Repository } from 'typeorm';
import { EmailJobService } from '../../email/email-job.service';
import { GpuUtilityService } from '../../gpu-utility/services/gpu-utility/gpu-utility.service';
import { NotificationGateway, NotifyService } from '../../notification';
import { PubSubService } from '../../pubsub/pubsub.service';
import { User } from '../../user';
import { DeallocationEventDto, OfflineWarningEventDto } from '../dto';
import { GpusRented } from '../entities';
import { CostService } from '../service/cost.service';
import { GpuUsageSessionsService } from '../service/gpu-usage-sessions.service';
import { GpuService } from '../service/gpu.service';

@ApiTags('webhooks')
@Controller('gpus/webhook')
export class GpuWebhook {
  constructor(
    @InjectRepository(GpusRented)
    readonly gpusRentedRepository: Repository<GpusRented>,
    @InjectRepository(User)
    readonly userRepository: Repository<User>,
    private readonly notificationGateway: NotificationGateway,
    private readonly notifyService: NotifyService,
    private readonly logger: Logger,
    private readonly gpuService: GpuService,
    private readonly costService: CostService,
    private readonly gpuUsageSessionsService: GpuUsageSessionsService,
    private readonly gpuUtilityService: GpuUtilityService,
    private readonly emailJobService: EmailJobService,
    private readonly pubSubService: PubSubService,
  ) {}

  // eslint-disable-next-line @darraghor/nestjs-typed/api-methods-should-be-guarded
  @Post('deallocation')
  async handleDeallocationEvent(@Body() deallocationEvent: DeallocationEventDto): Promise<IGpuWebhookResponse> {
    try {
      const { deallocated_at, hotkey, uuid } = deallocationEvent;
      this.logger.log(
        `Webhook: Deallocation process started for hotkey '${hotkey}' and deallocated_at '${deallocated_at} and allocation_uuid '${uuid}'. Timestamp: ${new Date().toISOString()}`,
        {
          hotkey,
          allocation_uuid: uuid,
          deallocated_at,
        },
      );
      let gpuRented = await this.gpusRentedRepository.findOne({
        where: { allocation_uuid: uuid, hotkey: hotkey, is_allocated: true },
        relations: ['user'],
      });

      if (!gpuRented) {
        this.logger.log(
          `Webhook Error: Deallocation failed for hotkey '${hotkey}' and deallocated_at '${deallocated_at}' due to missing allocation_uuid '${uuid}'. Timestamp: ${new Date().toISOString()}`,
          {
            hotkey,
            allocation_uuid: uuid,
            deallocated_at,
          },
        );
        return {
          status: 'error',
          message: 'allocation_uuid not found',
          statusCode: 200,
        };
      }
      //logger meta data
      const logMetaData = {
        hotkey,
        allocation_uuid: uuid,
        deallocated_at,
        gpu_rented_id: gpuRented.gpu_rented_id,
      };
      this.logger.log(`Webhook: Deallocation process started for hotkey '${hotkey}'`, logMetaData);

      gpuRented = await this.gpuService.deallocateByWebhook(gpuRented, gpuRented.user, deallocated_at);
      const user = gpuRented.user;

      this.logger.log(`Webhook : Deallocation success for hotkey '${hotkey}'`, logMetaData);

      // Publish deallocation event to Pub/Sub
      try {
        await this.pubSubService.publishDeallocationEvent({
          eventType: 'deallocation',
          timestamp: new Date().toISOString(),
          data: {
            hotkey,
            uuid,
            deallocated_at: deallocated_at.toISOString(),
            gpu_rented_id: gpuRented.gpu_rented_id,
            user_id: user.user_id,
          },
        });
      } catch (pubSubError) {
        this.logger.error('Failed to publish deallocation event to Pub/Sub:', pubSubError);
        // Don't fail the webhook if Pub/Sub fails
      }

      this.notificationGateway.updateBalance(user.user_id, user.balance);
      const currentCost = await this.costService.getCurrentCost(user.user_id);
      this.notificationGateway.updateCurrentCost(user.user_id, currentCost);

      this.notificationGateway.removeDeallocatedGpu(user.user_id, gpuRented.gpu_rented_id);
      await this.notifyService.notifyUser(
        user.user_id,
        NotificationType.GPU_DEALLOCATED,
        `Your GPU (${gpuRented.name}) has been deallocated externally.`,
      );
      this.emailJobService.sendExternalDeallocationEmail(
        user.email,
        `${user.first_name} ${user.last_name}`,
        gpuRented.name,
        gpuRented.gpu_model,
        gpuRented.deallocated_at,
      );
      const userCurrentRunway = await this.gpuUtilityService.calculateCurrentRunwayTime(user.user_id);
      this.notificationGateway.updateCurrentRunway(user.user_id, userCurrentRunway);

      return {
        status: 'success',
        message: 'webhook notify is success',
        statusCode: 200,
      };
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        statusCode: 500,
      };
    }
  }

  // eslint-disable-next-line @darraghor/nestjs-typed/api-methods-should-be-guarded
  @Post('status-change-warning')
  async handleOfflineWarningEvent(@Body() offlineWarningEvent: OfflineWarningEventDto): Promise<IGpuWebhookResponse> {
    try {
      const { status_change_at, hotkey, status, uuid } = offlineWarningEvent;
      const gpuRented = await this.gpusRentedRepository.findOne({
        where: { hotkey: hotkey, allocation_uuid: uuid, is_allocated: true },
        relations: ['user'],
      });

      if (!gpuRented) {
        return {
          status: 'error',
          message: 'gpu not found',
          statusCode: 200,
        };
      }
      if (status === OFFLINE && !this.wasOffLine(gpuRented)) {
        gpuRented.is_payment_suspended = true;
        gpuRented.offline_status_received_at = new Date(status_change_at);
        gpuRented.online_status_received_at = null;
        gpuRented.status = status;
        await this.gpuService.updateRentedGpu(gpuRented.gpu_rented_id, {
          is_payment_suspended: gpuRented.is_payment_suspended,
          offline_status_received_at: gpuRented.offline_status_received_at,
          online_status_received_at: gpuRented.online_status_received_at,
          status: gpuRented.status,
        });

        await this.gpuUsageSessionsService.updateGpusUsage(
          gpuRented.gpu_rented_id,
          IGpuPaymentStatus.OFFLINE,
          new Date(status_change_at),
          0,
        );

        this.notifyService.notifyUser(
          gpuRented.user.user_id,
          NotificationType.GPU_GET_OFFLINE,
          `Your GPU (${gpuRented.name}) went offline. We have suspended the payment for this GPU.`,
        );
        this.emailJobService.sendGpuGoneOfflineEmail(
          gpuRented.user.email,
          `${gpuRented.user.first_name} ${gpuRented.user.last_name}`,
          gpuRented.name,
          gpuRented.gpu_model,
        );

        this.notificationGateway.updateRentedGpu(gpuRented.user.user_id, gpuRented);
        this.notifyCurrentCost(gpuRented.user.user_id);

        this.logger.log(
          `Webhook : Received offline status for gpu'${
            gpuRented.gpu_rented_id
          }' and hotkey '${hotkey}' and allocation_uuid '${uuid}' and status_change_at '${status_change_at}'on ${new Date().toISOString()}`,
          {
            gpu_rented_id: gpuRented.gpu_rented_id,
            hotkey,
            status_change_at,
          },
        );
      } else if (status === ONLINE && !this.wasOnLine(gpuRented)) {
        gpuRented.is_payment_suspended = false;
        gpuRented.online_status_received_at = new Date(status_change_at);
        gpuRented.offline_status_received_at = null;
        gpuRented.status = status;
        await this.gpuService.updateRentedGpu(gpuRented.gpu_rented_id, {
          is_payment_suspended: gpuRented.is_payment_suspended,
          online_status_received_at: gpuRented.online_status_received_at,
          offline_status_received_at: gpuRented.offline_status_received_at,
          status: gpuRented.status,
        });

        await this.gpuUsageSessionsService.updateGpusUsage(gpuRented.gpu_rented_id, IGpuPaymentStatus.GRACE, new Date(status_change_at), 0);

        this.notifyService.notifyUser(
          gpuRented.user.user_id,
          NotificationType.GPU_GET_ONLINE,
          `Your GPU (${gpuRented.name}) is now online. We will resume the payment for this GPU.`,
        );

        this.emailJobService.sendGpuBackOnlineEmail(
          gpuRented.user.email,
          `${gpuRented.user.first_name} ${gpuRented.user.last_name}`,
          gpuRented.name,
          gpuRented.gpu_model,
        );

        this.notificationGateway.updateRentedGpu(gpuRented.user.user_id, gpuRented);
        this.notifyCurrentCost(gpuRented.user.user_id);

        this.logger.log(
          `Webhook : Received online status for gpu'${
            gpuRented.gpu_rented_id
          }' and hotkey '${hotkey}' and allocation_uuid '${uuid}' and status_change_at '${status_change_at}'on ${new Date().toISOString()}`,
          {
            gpu_rented_id: gpuRented.gpu_rented_id,
            hotkey,
            allocation_uuid: uuid,
            status_change_at,
          },
        );
      } else {
        this.logger.log(
          `Webhook : Received ${status} for gpu '${
            gpuRented.gpu_rented_id
          }' and hotkey '${hotkey}' and allocation_uuid '${uuid}' and status_change_at '${status_change_at}' on ${new Date().toISOString()}`,
          {
            gpu_rented_id: gpuRented.gpu_rented_id,
            hotkey,
            allocation_uuid: uuid,
            status_change_at,
          },
        );
      }

      return {
        status: 'success',
        message: 'webhook notify is success',
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error(`Error while handling offline warning event for hotkey ${offlineWarningEvent.hotkey} ${error.message}`, error);
      return {
        status: 'error',
        message: error.message,
        statusCode: 500,
      };
    }
  }

  private wasOnLine(gpuRented: GpusRented): boolean {
    return gpuRented.status === ONLINE;
  }

  private wasOffLine(gpuRented: GpusRented): boolean {
    return gpuRented.status === OFFLINE;
  }

  private async notifyCurrentCost(user_id: string) {
    try {
      const currentCost = await this.costService.getCurrentCost(user_id);
      this.notificationGateway.updateCurrentCost(user_id, currentCost);
    } catch (error) {
      this.logger.error(`Error while notifying current cost for user ${user_id}`, error);
    }
  }
}
