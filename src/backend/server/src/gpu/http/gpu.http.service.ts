import { parse } from 'querystring';
import { HttpService } from '@nestjs/axios';
import { Injectable, HttpException, InternalServerErrorException, NotFoundException, Logger } from '@nestjs/common';
import {
  IAllocatedGpuResource,
  IAllocatedHotkeysApiResponse,
  IAllocatedGpuResourceList,
  IDeallocateApiSuccessResource,
  IGpuApiErrorResource,
  IGpuResourceSearch,
  IGpuSpecsApiResponse,
  IGpuStats,
  IMinerStatus,
  IMinerStatusApiResponse,
  ISearchGpuResource,
  IGpuModelCount,
} from '@neural/models';
import { AxiosError } from 'axios';
import { catchError, firstValueFrom, of, take } from 'rxjs';
import { SSH_PUBLIC_KEY_TYPE_USER } from '../../ssh/constants/ssh-constants';

@Injectable()
export class GpuHttpService {
  private readonly logger = new Logger(GpuHttpService.name);

  constructor(private readonly httpService: HttpService) {}

  async getAvailableGpuResource(body: IGpuResourceSearch) {
    let url = `/list/resources_wandb`;
    const { page_number: page_number, page_size: page_size, ...rest } = body;

    if (page_number && page_size) {
      url += `?page_number=${page_number}&page_size=${page_size}`;
    }

    try {
      const { data } = await firstValueFrom(
        this.httpService.post<ISearchGpuResource>(url, rest).pipe(
          catchError((error: AxiosError<IGpuApiErrorResource>) => {
            if (error.response && error.response.status && error.response.data && error.response?.data.err_detail) {
              throw new HttpException(error.response?.data.err_detail, error.response?.status);
            }
            this.logger.error(`Error getting available GPU resource: ${error.message}`, error?.stack || {}, { body });
            throw new InternalServerErrorException(error.message);
          }),
        ),
      );

      if (!data.success) {
        throw new InternalServerErrorException(data.message);
      }
      return data;
    } catch (error) {
      this.logger.error(`Error getting available GPU resource: ${error.message}`, error?.stack || {}, { body });
      throw new InternalServerErrorException(error.message);
    }
  }

  async deployGpuResource(hotkey: string, ssh_key: string | null) {
    const deployUrl = ssh_key
      ? `/service/allocate_hotkey?hotkey=${hotkey}&ssh_key=${encodeURIComponent(ssh_key)}`
      : `/service/allocate_hotkey?hotkey=${hotkey}`;
    const { data } = await firstValueFrom(
      this.httpService.post<IAllocatedGpuResource>(deployUrl).pipe(
        catchError((error: AxiosError<IGpuApiErrorResource>) => {
          const status = error.response?.status || 500;
          if (error.response?.data?.err_detail === 'Requested resource is not available.') {
            error.response.data.err_detail = 'The requested resource is not available. Please try again with a different GPU resource.';
          }
          const errorMessage = error.response?.data?.err_detail || error.message || 'Unknown error occurred';

          this.logger.error(
            `Error deploying GPU resource, method deployGpuResource: ${errorMessage}`,
            JSON.stringify(
              {
                url: error.config?.url,
                data: error.config?.data,
                queryParams: parse(error.config?.url?.split('?')[1] || ''),
                stack: error.stack?.split('\n').map((line) => line.trim()),
              },
              null,
              2,
            ),
          );

          throw new HttpException(errorMessage, status);
        }),
      ),
    );

    if (!data.success) {
      throw new InternalServerErrorException(data.message || 'Deployment failed');
    }

    return data;
  }

  async getGpuResourceDetail(hotkey: string) {
    const { data } = await firstValueFrom(
      this.httpService.post<IGpuSpecsApiResponse>(`/list/specs?hotkey=${hotkey}`).pipe(
        catchError((error: AxiosError<IGpuApiErrorResource>) => {
          if (error.response?.data) {
            throw new HttpException(error.response?.data.err_detail, error.response?.status);
          }
          this.logger.error(`Error getting GPU resource detail: ${error.message}`, error?.stack || {}, { hotkey });
          throw new InternalServerErrorException('Error deploying GPU resource');
        }),
      ),
    );

    if (!data.success) {
      throw new InternalServerErrorException(data.message);
    }

    if (!data.data?.page_items?.length) {
      throw new NotFoundException('Resource not found');
    }

    return data;
  }

  async deallocateGpuResource(hotkey: string, uuid_key: string) {
    const { data } = await firstValueFrom(
      this.httpService
        .post<IDeallocateApiSuccessResource>(`/service/deallocate?hotkey=${hotkey}&uuid_key=${uuid_key}&notify_flag=false`)
        .pipe(
          catchError((error: AxiosError<IGpuApiErrorResource>) => {
            if (error.response?.data) {
              if (error.response?.data.err_detail !== 'No allocation details found for the provided hotkey.') {
                throw new HttpException(error.response?.data.err_detail, error.response?.status);
              }
              return of(error.response).pipe(take(1));
            }
            this.logger.error(`Error deallocating GPU resource: ${error.message}`, error?.stack || {}, { hotkey, uuid_key });
            throw new InternalServerErrorException('Error deallocating GPU resource');
          }),
        ),
    );

    return data;
  }

  async checkMinerStatus(hotkeys: string[], queryVersion = false): Promise<IMinerStatus[]> {
    const url = `/service/check_miner_status?query_version=${queryVersion}`;
    const { data } = await firstValueFrom(
      this.httpService.post<IMinerStatusApiResponse>(url, hotkeys).pipe(
        catchError((error: AxiosError<IGpuApiErrorResource>) => {
          if (error.response?.data) {
            throw new HttpException(error.response?.data.err_detail, error.response?.status);
          }
          this.logger.error(`Error checking miner status: ${error.message}`, error?.stack || {}, { hotkeys });
          throw new InternalServerErrorException('Error checking miner status');
        }),
      ),
    );

    if (!data.success) {
      throw new InternalServerErrorException(data.message);
    }
    if (data.data && data.data.length) {
      data.data.map((item) => {
        this.logger.log(`Received '${item.status}' for '${item.hotkey}' at  ${new Date().toISOString()}`);
      });
    }
    return data.data;
  }

  async getGpuStats(): Promise<IGpuStats> {
    const { data } = await firstValueFrom(
      this.httpService.post<IGpuStats>(`/list/resources_wandb?stats=true`).pipe(
        catchError((error: AxiosError<IGpuApiErrorResource>) => {
          if (error.response?.data) {
            throw new HttpException(error.response?.data.err_detail, error.response?.status);
          }
          this.logger.error(`Error Getting  Gpu Stats: ${error.message}`, error?.stack || {});
          throw new InternalServerErrorException('Error Getting  Gpu Stats');
        }),
      ),
    );

    if (!data.success) {
      throw new InternalServerErrorException(data.message);
    }

    return data;
  }

  async restartGpuResource(hotkey: string, uuid_key: string) {
    const { data } = await firstValueFrom(
      this.httpService.post<IDeallocateApiSuccessResource>(`/service/restart_docker?hotkey=${hotkey}&uuid_key=${uuid_key}`).pipe(
        catchError((error: AxiosError<IGpuApiErrorResource>) => {
          if (error.response?.data) {
            throw new HttpException(error.response?.data.err_detail, error.response?.status);
          }
          this.logger.error(`Error restarting GPU resource: ${error.message}`, error?.stack || {}, { hotkey, uuid_key });
          throw new InternalServerErrorException('Error restarting GPU resource');
        }),
      ),
    );

    if (!data.success) {
      throw new InternalServerErrorException(data.message);
    }

    return data;
  }

  async changeSshKey(hotkey: string, uuid_key: string, ssh_key: string, key_type: string = SSH_PUBLIC_KEY_TYPE_USER) {
    const encodedSshKey = encodeURIComponent(ssh_key);
    const { data } = await firstValueFrom(
      this.httpService
        .post<IDeallocateApiSuccessResource>(
          `/service/exchange_docker_key?hotkey=${hotkey}&uuid_key=${uuid_key}&ssh_key=${encodedSshKey}&key_type=${key_type}`,
        )
        .pipe(
          catchError((error: AxiosError<IGpuApiErrorResource>) => {
            if (error.response?.data) {
              throw new HttpException(error.response?.data.err_detail, error.response?.status);
            }
            this.logger.error(`Error changing ssh key: ${error.message}`, error?.stack || {}, { hotkey, uuid_key });
            throw new InternalServerErrorException('Error changing ssh key');
          }),
        ),
    );

    if (!data.success) {
      throw new InternalServerErrorException(data.message);
    }

    return data;
  }

  async getAllocatedHotkeys(): Promise<IAllocatedHotkeysApiResponse> {
    const { data } = await firstValueFrom(
      this.httpService.post<IAllocatedHotkeysApiResponse>('/list/allocated_hotkeys').pipe(
        catchError((error: AxiosError<IGpuApiErrorResource>) => {
          if (error.response?.data) {
            throw new HttpException(error.response?.data.err_detail, error.response?.status);
          }
          this.logger.error(`Error getting allocated hotkeys: ${error.message}`, error?.stack || {});
          throw new InternalServerErrorException('Error getting allocated hotkeys');
        }),
      ),
    );

    if (!data.success) {
      throw new InternalServerErrorException(data.message);
    }

    if (!data.data?.length) {
      data.data = [];
    }

    return data;
  }

  async getRegisterApiAllocatedGpuResources() {
    const { data } = await firstValueFrom(
      this.httpService.post<IAllocatedGpuResourceList>('/list/allocations_sql').pipe(
        catchError((error: AxiosError<IGpuApiErrorResource>) => {
          if (error.response?.data) {
            throw new HttpException(error.response?.data.err_detail, error.response?.status);
          }
          this.logger.error(`Error getting registered api allocated gpu resources: ${error.message}`, error?.stack || {});
          throw new InternalServerErrorException('Error getting registered api allocated gpu resources');
        }),
      ),
    );

    if (!data.success) {
      throw new InternalServerErrorException(data.message);
    }

    return data;
  }
  async getGpuModelCount(model: string, cpu_count: number, ram_size: number) {
    const url = `/list/count_all_by_model?model=${model}&cpu_count=${cpu_count}&ram_size=${ram_size}`;

    const { data } = await firstValueFrom(
      this.httpService.post<IGpuModelCount>(url).pipe(
        catchError((error: AxiosError<IGpuApiErrorResource>) => {
          if (error.response?.data) {
            throw new HttpException(error.response?.data.err_detail, error.response?.status);
          }
          this.logger.error(`Error getting gpu model count from register api: ${error.message}`, error?.stack || {}, {
            model,
            cpu_count,
            ram_size,
          });
          throw new InternalServerErrorException('Error getting gpu model count from register api');
        }),
      ),
    );

    return data;
  }
}
