import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { GpuDeallocateByEnum, IUserNotification, NotificationType } from '@neural/models';
import { differenceInSeconds, differenceInMinutes, subMinutes } from 'date-fns';
import { Repository, EntityManager } from 'typeorm';
import { EmailJobService } from '../../email/email-job.service';
import { InstanceIdService } from '../../instance-id';
import { LeaderElectionService } from '../../leader-election';
import { NotificationGateway, NotifyService } from '../../notification';
import { PaymentService } from '../../payment';
import { HotkeyAllocationReportingService } from '../../reporting/service/hotkey-allocation-reporting.service';
import { User } from '../../user';
import { ALLOCATION_CHECKER_JOB } from '../constants/jobs.constants';
import { GpusRented } from '../entities';
import { GpuHttpService } from '../http/gpu.http.service';
import { CostService } from '../service/cost.service';
import { GpuUsageSessionsService } from '../service/gpu-usage-sessions.service';
import { GpuService } from '../service/gpu.service';

@Injectable()
export class AllocationCheckerService {
  constructor(
    private readonly gpuHttpService: GpuHttpService,
    private readonly notificationGateway: NotificationGateway,
    private readonly notifyService: NotifyService,
    private readonly costService: CostService,
    private readonly logger: Logger,
    @InjectRepository(GpusRented)
    readonly gpusRentedRepository: Repository<GpusRented>,
    @InjectRepository(User)
    readonly userRepository: Repository<User>,
    private readonly paymentService: PaymentService,
    private readonly gpuService: GpuService,
    private readonly gpuUsageSessionsService: GpuUsageSessionsService,
    private readonly instanceIdService: InstanceIdService,
    private readonly leaderElectionService: LeaderElectionService,
    private readonly hotkeyAllocationJobService: HotkeyAllocationReportingService,
    private readonly emailJobService: EmailJobService,
  ) {}

  @Cron('30 */5 * * * *') // Every 5 minutes starting from 30th second
  async handleCron() {
    if (process.env.NODE_ENV === 'development') return; // Disable cron for dev env.

    // Attempt to elect this instance as the leader
    const jobName = ALLOCATION_CHECKER_JOB;
    const instanceId = await this.instanceIdService.getInstanceId();
    const startTime = Date.now(); // Capture start time
    let renewLockInterval: NodeJS.Timeout | null = null;

    const isLeader = await this.leaderElectionService.electLeader(jobName, instanceId);
    if (!isLeader) {
      return; // Skip execution if this instance is not the leader
    }

    try {
      // Start lock renewal process
      renewLockInterval = this.leaderElectionService.startLockRenewal(jobName, instanceId);

      const [dashboardRental, registerApiAllocations] = await Promise.all([
        this.gpusRentedRepository.find({
          where: {
            is_allocated: true,
          },
          relations: ['user'],
        }),
        this.gpuHttpService.getAllocatedHotkeys(),
      ]);

      if (!registerApiAllocations.data.length) {
        registerApiAllocations.data = [];
      }

      const allocatedHotkeysSet = new Set(registerApiAllocations.data || []);
      const rentalMap = new Map<string, GpusRented>();
      const dashboardAllocationsHotkeys: string[] = [];
      dashboardRental.forEach((rentedGpu) => {
        rentalMap.set(rentedGpu.gpu_rented_id, rentedGpu);
        dashboardAllocationsHotkeys.push(rentedGpu.gpu_rented_id);
      });

      this.hotkeyAllocationJobService.doReporting(dashboardAllocationsHotkeys, registerApiAllocations);

      for (const rentedGpu of dashboardRental) {
        const isHotkeyMissing = !allocatedHotkeysSet.has(rentedGpu.hotkey);

        if (isHotkeyMissing) {
          rentedGpu.allocation_fault_count = rentedGpu.allocation_fault_count === null ? 0 : rentedGpu.allocation_fault_count + 1;

          if (rentedGpu.allocation_fault_count >= 2) {
            const user = rentedGpu.user;
            rentedGpu.is_allocated = false;
            rentedGpu.deallocated_by = GpuDeallocateByEnum.ALLOCATION_CHECKER;
            rentedGpu.deallocated_at = new Date();

            await this.gpuService.updateRentedGpu(rentedGpu.gpu_rented_id, {
              is_allocated: rentedGpu.is_allocated,
              deallocated_by: rentedGpu.deallocated_by,
              deallocated_at: rentedGpu.deallocated_at,
              allocation_fault_count: rentedGpu.allocation_fault_count,
            });

            this.logger.log(
              `GPU ${rentedGpu.gpu_rented_id} with hotkey ${rentedGpu.hotkey} and name ${rentedGpu.name} deallocated by allocation checker job due to 3 consecutive missing checks.`,
              {
                gpu_rented_id: rentedGpu.gpu_rented_id,
                name: rentedGpu.name,
                hotkey: rentedGpu.hotkey,
                is_allocated: rentedGpu.is_allocated,
                deallocated_by: rentedGpu.deallocated_by,
                deallocated_at: rentedGpu.deallocated_at,
                allocation_fault_count: rentedGpu.allocation_fault_count,
              },
            );

            await this.paymentService.createOrUpdateRentalPayment(user.user_id, rentedGpu);
            await this.gpuUsageSessionsService.endGpuUsageOnDeallocation(rentedGpu.gpu_rented_id, rentedGpu.deallocated_at, 0);

            if (user) {
              const now = new Date();
              const fifteenMinutesAgo = subMinutes(now, 15);
              let minutes = 0;
              let refundDone = false;

              if (rentedGpu.last_payment_date && rentedGpu.last_payment_date > fifteenMinutesAgo) {
                const secondsDifference = differenceInSeconds(fifteenMinutesAgo, rentedGpu.last_payment_date);
                const hoursDifference = secondsDifference / 3600;
                minutes = differenceInMinutes(fifteenMinutesAgo, rentedGpu.last_payment_date);
                const cost = rentedGpu.price * hoursDifference;
                this.userRepository.update(user.user_id, {
                  balance: () => `balance + ${cost * 100}`,
                });
                refundDone = true;
              }

              this.notificationGateway.removeDeallocatedGpu(user.user_id, rentedGpu.gpu_rented_id);
              const currentCost = await this.costService.getCurrentCost(user.user_id);
              this.notificationGateway.updateCurrentCost(user.user_id, currentCost);
              const notificationMessage = refundDone
                ? `Your GPU (${rentedGpu.name}) has been deallocated externally. You have been refunded for the last ${minutes} minutes cost.`
                : `Your GPU (${rentedGpu.name}) has been deallocated externally.`;
              this.notifyService.notifyUser(user.user_id, NotificationType.GPU_DEALLOCATED, notificationMessage);
              this.emailJobService.sendExternalDeallocationEmail(
                user.email,
                `${user.first_name} ${user.last_name}`,
                rentedGpu.name,
                rentedGpu.gpu_model,
                rentedGpu.deallocated_at,
              );
            }
          } else {
            await this.gpuService.updateRentedGpu(rentedGpu.gpu_rented_id, {
              allocation_fault_count: rentedGpu.allocation_fault_count,
            });
          }
        } else if (rentedGpu.allocation_fault_count !== null) {
          rentedGpu.allocation_fault_count = null;
          await this.gpuService.updateRentedGpu(rentedGpu.gpu_rented_id, {
            allocation_fault_count: rentedGpu.allocation_fault_count,
          });
        }
      }
      await this.leaderElectionService.handleJobSuccess(jobName, startTime, instanceId);
    } catch (error) {
      await this.leaderElectionService.handleDeadLetter(jobName, instanceId, error); // Move to DLQ
    } finally {
      await this.leaderElectionService.releaseLock(jobName, instanceId);
      if (renewLockInterval) {
        clearInterval(renewLockInterval);
      }
    }
  }
}
