import { Injectable, Inject, Logger } from '@nestjs/common';
import { CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { IBalanceMonitorConfig } from '@neural/models';
import { <PERSON>ronJob } from 'cron';
import { InstanceIdService } from '../../instance-id';
import { LeaderElectionService } from '../../leader-election';
import { BALANCE_MONITOR_JOB } from '../constants/jobs.constants';
import { BALANCE_MONITOR_CONFIG } from '../gpu.token';
import { GpuCostMonitorService } from '../service/gpu-cost-monitor.service';

@Injectable()
export class BalanceMonitorService {
  private readonly logger = new Logger(BalanceMonitorService.name);
  private cronJob: CronJob;
  private isRunning = false; // Mutex flag to prevent overlapping executions

  constructor(
    private gpuCostMonitorService: GpuCostMonitorService,
    private schedulerRegistry: SchedulerRegistry,
    @Inject(BALANCE_MONITOR_CONFIG)
    private readonly balanceMonitorConfig: IBalanceMonitorConfig,
    private readonly leaderElectionService: LeaderElectionService, // Inject LeaderElectionService
    private readonly instanceIdService: InstanceIdService, // Inject LeaderElectionService
  ) {
    this.initializeCronJob();
  }

  async handleCron() {
    // Attempt to elect this instance as the leader
    const jobName = BALANCE_MONITOR_JOB;
    const instanceId = await this.instanceIdService.getInstanceId();
    const startTime = Date.now(); // Capture start time
    let renewLockInterval: NodeJS.Timeout | null = null;

    const isLeader = await this.leaderElectionService.electLeader(jobName, instanceId);
    if (!isLeader) {
      return; // Skip execution if this instance is not the leader
    }
    try {
      // Start lock renewal process
      renewLockInterval = this.leaderElectionService.startLockRenewal(jobName, instanceId);
      await this.gpuCostMonitorService.monitorAndTopUpBalances();
      await this.leaderElectionService.handleJobSuccess(jobName, startTime, instanceId);
    } catch (error) {
      await this.leaderElectionService.handleDeadLetter(jobName, instanceId, error); // Move to DLQ
    } finally {
      await this.leaderElectionService.releaseLock(jobName, instanceId);
      if (renewLockInterval) {
        clearInterval(renewLockInterval);
      }
    }
  }

  private initializeCronJob() {
    if (process.env.NODE_ENV === 'development') return; //Disable cron for dev env.
    this.logger.log('Initializing balance monitor job');
    const cronExpression = this.getBalanceMonitorIntervalCronExpression();

    this.cronJob = new CronJob(cronExpression, async () => {
      await this.handleCron();
    });

    this.schedulerRegistry.addCronJob('BALANCE_MONITOR_INTERVAL_CRON', this.cronJob);
    this.cronJob.start();
    this.logger.log(`Balance monitor job started with cron expression: ${cronExpression}`);
  }

  private getBalanceMonitorIntervalCronExpression() {
    const interval = this.balanceMonitorConfig.balanceMonitorIntervalInMinutes;

    if (interval === 60) {
      return CronExpression.EVERY_HOUR;
    }

    if (interval === 1) {
      return CronExpression.EVERY_MINUTE;
    }

    return `0 */${interval} * * * *`;
  }
}
