import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { getStartAndEndOfDay } from '@neural/models';
import { Between, Repository } from 'typeorm';
import { InstanceIdService } from '../../instance-id';
import { LeaderElectionService } from '../../leader-election';
import { DAILY_COUNTER_GPU_JOB } from '../constants/jobs.constants';
import { GpuDailyCount, GpusRented } from '../entities';
import { GpuHttpService } from '../http/gpu.http.service';

@Injectable()
export class DailyGpuCounterService {
  constructor(
    private readonly gpuHttpService: GpuHttpService,
    private readonly instanceIdService: InstanceIdService,
    private readonly leaderElectionService: LeaderElectionService,
    private readonly logger: Logger,
    @InjectRepository(GpuDailyCount)
    readonly gpuDailyCountRepository: Repository<GpuDailyCount>,
    @InjectRepository(GpusRented)
    readonly gpusRentedRepository: Repository<GpusRented>,
  ) {}

  @Cron(CronExpression.EVERY_HOUR)
  async handleCron() {
    if (process.env.NODE_ENV === 'development') return; //Disable cron for dev env.

    // Attempt to elect this instance as the leader
    const jobName = DAILY_COUNTER_GPU_JOB;
    const instanceId = await this.instanceIdService.getInstanceId();
    const startTime = Date.now(); // Capture start time
    let renewLockInterval: NodeJS.Timeout | null = null;

    const isLeader = await this.leaderElectionService.electLeader(jobName, instanceId);
    if (!isLeader) {
      return; // Skip execution if this instance is not the leader
    }
    try {
      // Start lock renewal process
      renewLockInterval = this.leaderElectionService.startLockRenewal(jobName, instanceId);

      const stats = await this.gpuHttpService.getGpuStats();
      const now = new Date();
      const { startDate, endDate } = getStartAndEndOfDay(now);

      const existing = await this.gpuDailyCountRepository.findOne({
        where: {
          date: Between(startDate, endDate),
        },
      });

      const totalRentalsToday = await this.gpusRentedRepository.count({
        where: {
          created_at: Between(startDate, endDate),
        },
      });
      if (existing) {
        if (stats.data.stats.available > existing.total_available_gpu || totalRentalsToday > existing.total_rented_gpu) {
          existing.total_available_gpu = stats.data.stats.available;
          existing.total_rented_gpu = totalRentalsToday;
          await this.gpuDailyCountRepository.save(existing);
        }
        return;
      }

      const newGpuDailyCount = new GpuDailyCount();
      newGpuDailyCount.date = now;
      newGpuDailyCount.total_available_gpu = stats.data.stats.available;
      newGpuDailyCount.total_rented_gpu = totalRentalsToday;

      await this.gpuDailyCountRepository.save(newGpuDailyCount);
      await this.leaderElectionService.handleJobSuccess(jobName, startTime, instanceId);
    } catch (error) {
      await this.leaderElectionService.handleDeadLetter(jobName, instanceId, error); // Move to DLQ
    } finally {
      await this.leaderElectionService.releaseLock(jobName, instanceId);
      if (renewLockInterval) {
        clearInterval(renewLockInterval);
      }
    }
  }
}
