import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import {
  convertToThreeDecimals,
  convertToTwoDecimals,
  getAverage,
  getHoursInDateRange,
  getMedian,
  getTheStartOfDay,
  GPU_CHART_DATA_FORMAT,
  IGpuChartsCumulativeStats,
  IGpuChartsData,
  IGpusModels,
  IGpuStatBasicInfo,
  IPeriod,
  periodToDays,
} from '@neural/models';
import { addDays, format, subDays } from 'date-fns';
import { Repository } from 'typeorm';
import { InstanceIdService } from '../../instance-id';
import { LeaderElectionService } from '../../leader-election';
import { GPU_STATS_JOB } from '../constants/jobs.constants';
import { GpuHwPricing, G<PERSON>Charts, GpusModels, GpusRented, GpuStats } from '../entities';
import { GpuHttpService } from '../http/gpu.http.service';

@Injectable()
export class GpuStatsService implements OnModuleInit {
  private readonly gpuModelToGpuCountMap: Map<string, number> = new Map<string, number>();
  private readonly gpuStatOf7dToBasicInfoMap: Map<number, IGpuStatBasicInfo> = new Map<number, IGpuStatBasicInfo>();
  private readonly gpuStatOf30dToBasicInfoMap: Map<number, IGpuStatBasicInfo> = new Map<number, IGpuStatBasicInfo>();
  private readonly gpuStatOf90dToBasicInfoMap: Map<number, IGpuStatBasicInfo> = new Map<number, IGpuStatBasicInfo>();
  private readonly gpuStatOf1yToBasicInfoMap: Map<number, IGpuStatBasicInfo> = new Map<number, IGpuStatBasicInfo>();

  constructor(
    @InjectRepository(GpusCharts)
    readonly gpusChartsRepository: Repository<GpusCharts>,
    @InjectRepository(GpusModels)
    readonly gpusModelsRepository: Repository<GpusModels>,
    @InjectRepository(GpuStats)
    readonly gpuStatsRepository: Repository<GpuStats>,
    @InjectRepository(GpuHwPricing)
    readonly gpuHwPricingRepository: Repository<GpuHwPricing>,
    @InjectRepository(GpusRented)
    readonly gpusRentedRepository: Repository<GpusRented>,

    private readonly gpuHttpService: GpuHttpService,
    private readonly instanceIdService: InstanceIdService,
    private readonly leaderElectionService: LeaderElectionService,
    private readonly logger: Logger,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleCron() {
    await this.runGpuStatsUpdate();
  }

  async onModuleInit() {
    try {
      const atListOneGpuStatsExists = await this.gpuStatsRepository.count();
      if (atListOneGpuStatsExists > 0) {
        return;
      }
      await this.runGpuStatsUpdate();
    } catch (error) {
      this.logger.error('Error while creating initial gpuStats', error);
    }
  }

  async runGpuStatsUpdate() {
    if (process.env.NODE_ENV === 'development') return; // Disable cron for dev env.

    // Attempt to elect this instance as the leader
    const jobName = GPU_STATS_JOB;
    const instanceId = await this.instanceIdService.getInstanceId();
    const startTime = Date.now(); // Capture start time
    let renewLockInterval: NodeJS.Timeout | null = null;

    const isLeader = await this.leaderElectionService.electLeader(jobName, instanceId);
    if (!isLeader) {
      return; // Skip execution if this instance is not the leader
    }

    try {
      // Start lock renewal process
      renewLockInterval = this.leaderElectionService.startLockRenewal(jobName, instanceId);

      const today = new Date();
      const gpuCharts = await this.getGpuCharts();
      const gpusHwPricing = await this.getGpuHwPricing();
      if (!gpusHwPricing) return;

      const startDates = {
        week: this.getStartDate(IPeriod.WEEK, today),
        month: this.getStartDate(IPeriod.MONTH, today),
        quarter: this.getStartDate(IPeriod.QUARTER, today),
        year: this.getStartDate(IPeriod.YEAR, today),
      };
      gpuCharts.map(async (gpuChart) => {
        const gpuModel: IGpusModels = {
          gpus_model_id: gpuChart.gpu_model_id,
          gpu_model: gpuChart.gpu_model,
          ram: gpuChart.ram,
          cpu: gpuChart.cpu,
        };

        const [
          { total_hours: total_hours_7d, total_earned: total_earned_7d, total_rented: total_rented_7d },
          { total_hours: total_hours_30d, total_earned: total_earned_30d, total_rented: total_rented_30d },
          { total_hours: total_hours_90d, total_earned: total_earned_90d, total_rented: total_rented_90d },
          { total_hours: total_hours_1y, total_earned: total_earned_1y, total_rented: total_rented_1y },
        ] = await Promise.all([
          this.getGpuChartsCumulativeStats(gpuChart.gpu_model_id, startDates.week),
          this.getGpuChartsCumulativeStats(gpuChart.gpu_model_id, startDates.month),
          this.getGpuChartsCumulativeStats(gpuChart.gpu_model_id, startDates.quarter),
          this.getGpuChartsCumulativeStats(gpuChart.gpu_model_id, startDates.year),
        ]);

        const util_7d = await this.calculateUtilizationRate(gpuModel, total_hours_7d, startDates.week, today);
        const util_30d = await this.calculateUtilizationRate(gpuModel, total_hours_30d, startDates.month, today);
        const util_90d = await this.calculateUtilizationRate(gpuModel, total_hours_90d, startDates.quarter, today);
        const util_1y = await this.calculateUtilizationRate(gpuModel, total_hours_1y, startDates.year, today);

        const earnings_7d = this.calculateWeeklyEarnings(total_earned_7d, IPeriod.WEEK);
        const earnings_30d = this.calculateWeeklyEarnings(total_earned_30d, IPeriod.MONTH);
        const earnings_90d = this.calculateWeeklyEarnings(total_earned_90d, IPeriod.QUARTER);
        const earnings_1y = this.calculateWeeklyEarnings(total_earned_1y, IPeriod.YEAR);

        const duration_7d = this.calculateWeeklyHours(total_hours_7d, IPeriod.WEEK);
        const duration_30d = this.calculateWeeklyHours(total_hours_30d, IPeriod.MONTH);
        const duration_90d = this.calculateWeeklyHours(total_hours_90d, IPeriod.QUARTER);
        const duration_1y = this.calculateWeeklyHours(total_hours_1y, IPeriod.YEAR);

        this.gpuStatOf7dToBasicInfoMap.set(gpuChart.gpu_model_id, {
          total_hours: total_hours_7d,
          total_earned: total_earned_7d,
          total_rented: total_rented_7d,
          utilization_rate: util_7d,
          earning: earnings_7d,
          duration: duration_7d,
        });

        this.gpuStatOf30dToBasicInfoMap.set(gpuChart.gpu_model_id, {
          total_hours: total_hours_30d,
          total_earned: total_earned_30d,
          total_rented: total_rented_30d,
          utilization_rate: util_30d,
          earning: earnings_30d,
          duration: duration_30d,
        });

        this.gpuStatOf90dToBasicInfoMap.set(gpuChart.gpu_model_id, {
          total_hours: total_hours_90d,
          total_earned: total_earned_90d,
          total_rented: total_rented_90d,
          utilization_rate: util_90d,
          earning: earnings_90d,
          duration: duration_90d,
        });

        this.gpuStatOf1yToBasicInfoMap.set(gpuChart.gpu_model_id, {
          total_hours: total_hours_1y,
          total_earned: total_earned_1y,
          total_rented: total_rented_1y,
          utilization_rate: util_1y,
          earning: earnings_1y,
          duration: duration_1y,
        });
      });

      const gpuStatus = await Promise.all(
        gpuCharts.map(async (gpuChart) => {
          const retention_rate = await this.calculateUserRetentionRate(gpuChart.gpu_model_id);
          const [gpuChartsData_7d, gpuChartsData_30d, gpuChartsData_90d, gpuChartsData_1y] = await Promise.all([
            this.getGpuChartsData(gpuChart.gpu_model_id, startDates.week),
            this.getGpuChartsData(gpuChart.gpu_model_id, startDates.month),
            this.getGpuChartsData(gpuChart.gpu_model_id, startDates.quarter),
            this.getGpuChartsData(gpuChart.gpu_model_id, startDates.year),
          ]);

          const [
            { average: avg_7d, median: median_7d },
            { average: avg_30d, median: median_30d },
            { average: avg_90d, median: median_90d },
            { average: avg_1y, median: median_1y },
          ] = await Promise.all([
            this.calculateGpuAverages(gpuChartsData_7d, startDates.week, IPeriod.WEEK),
            this.calculateGpuAverages(gpuChartsData_30d, startDates.month, IPeriod.MONTH),
            this.calculateGpuAverages(gpuChartsData_90d, startDates.quarter, IPeriod.QUARTER),
            this.calculateGpuAverages(gpuChartsData_1y, startDates.year, IPeriod.YEAR),
          ]);

          const gpuStats = new GpuStats();
          gpuStats.gpu_model_id = gpuChart.gpu_model_id;
          gpuStats.retention_rate = retention_rate;
          gpuStats.duration_7d = this.gpuStatOf7dToBasicInfoMap.get(gpuChart.gpu_model_id)?.duration || 0;
          gpuStats.duration_30d = this.gpuStatOf30dToBasicInfoMap.get(gpuChart.gpu_model_id)?.duration || 0;
          gpuStats.duration_90d = this.gpuStatOf90dToBasicInfoMap.get(gpuChart.gpu_model_id)?.duration || 0;
          gpuStats.duration_1y = this.gpuStatOf1yToBasicInfoMap.get(gpuChart.gpu_model_id)?.duration || 0;
          gpuStats.earnings_7d = this.gpuStatOf7dToBasicInfoMap.get(gpuChart.gpu_model_id)?.earning || 0;
          gpuStats.earnings_30d = this.gpuStatOf30dToBasicInfoMap.get(gpuChart.gpu_model_id)?.earning || 0;
          gpuStats.earnings_90d = this.gpuStatOf90dToBasicInfoMap.get(gpuChart.gpu_model_id)?.earning || 0;
          gpuStats.earnings_1y = this.gpuStatOf1yToBasicInfoMap.get(gpuChart.gpu_model_id)?.earning || 0;
          gpuStats.util_7d = this.gpuStatOf7dToBasicInfoMap.get(gpuChart.gpu_model_id)?.utilization_rate || 0;
          gpuStats.util_30d = this.gpuStatOf30dToBasicInfoMap.get(gpuChart.gpu_model_id)?.utilization_rate || 0;
          gpuStats.util_90d = this.gpuStatOf90dToBasicInfoMap.get(gpuChart.gpu_model_id)?.utilization_rate || 0;
          gpuStats.util_1y = this.gpuStatOf1yToBasicInfoMap.get(gpuChart.gpu_model_id)?.utilization_rate || 0;
          gpuStats.avg_7d = convertToThreeDecimals(avg_7d);
          gpuStats.avg_30d = convertToThreeDecimals(avg_30d);
          gpuStats.avg_90d = convertToThreeDecimals(avg_90d);
          gpuStats.avg_1y = convertToThreeDecimals(avg_1y);
          gpuStats.median_7d = convertToThreeDecimals(median_7d);
          gpuStats.median_30d = convertToThreeDecimals(median_30d);
          gpuStats.median_90d = convertToThreeDecimals(median_90d);
          gpuStats.median_1y = convertToThreeDecimals(median_1y);
          gpuStats.rentals_7d = this.gpuStatOf7dToBasicInfoMap.get(gpuChart.gpu_model_id)?.total_rented || 0;

          return gpuStats;
        }),
      );

      await Promise.all(
        gpuStatus.map(async (gpuStat) => {
          try {
            const gpuStatExists = await this.gpuStatsRepository.findOne({ where: { gpu_model_id: gpuStat.gpu_model_id } });
            if (gpuStatExists) {
              await this.gpuStatsRepository.update({ gpu_model_id: gpuStat.gpu_model_id }, gpuStat);
            } else {
              await this.gpuStatsRepository.save(gpuStat);
            }
          } catch (error) {
            this.logger.error(`Error updating gpuStats for gpu_model_id: ${gpuStat.gpu_model_id}: ${error}`);
          }
        }),
      );
      // Clear all the maps
      this.gpuModelToGpuCountMap.clear();
      this.gpuStatOf7dToBasicInfoMap.clear();
      this.gpuStatOf30dToBasicInfoMap.clear();
      this.gpuStatOf90dToBasicInfoMap.clear();
      this.gpuStatOf1yToBasicInfoMap.clear();

      await this.leaderElectionService.handleJobSuccess(jobName, startTime, instanceId);
    } catch (error) {
      await this.leaderElectionService.handleDeadLetter(jobName, instanceId, error); // Move to DLQ
    } finally {
      await this.leaderElectionService.releaseLock(jobName, instanceId);
      if (renewLockInterval) {
        clearInterval(renewLockInterval);
      }
    }
  }

  private async getGpuCharts(): Promise<IGpuChartsData[]> {
    try {
      return await this.gpusChartsRepository
        .createQueryBuilder('gpus_charts')
        .leftJoinAndSelect('gpus_charts.gpusModel', 'gpus_models')
        .select([
          'gpus_charts.gpus_chart_id AS gpus_chart_id',
          'gpus_charts.gpu_model_id AS gpu_model_id',
          'gpus_models.gpu_model AS gpu_model',
          'gpus_models.gpu_pricing_id AS gpu_pricing_id',
          'gpus_models.ram AS ram',
          'gpus_models.cpu AS cpu',
          'gpus_models.created_at AS created_at',
        ])
        .distinctOn(['gpus_charts.gpu_model_id'])
        .groupBy(
          'gpus_charts.gpus_chart_id, gpus_charts.gpu_model_id, gpus_models.gpu_model, gpus_models.gpu_pricing_id, gpus_models.ram, gpus_models.cpu, gpus_models.created_at',
        )
        .orderBy('gpus_charts.gpu_model_id', 'DESC')
        .getRawMany();
    } catch (error) {
      this.logger.error(`Error in getGpuCharts: ${error}`);
      return [];
    }
  }

  private async getGpuChartsData(gpuModelId: number, startDate: Date): Promise<GpusCharts[]> {
    try {
      return await this.gpusChartsRepository
        .createQueryBuilder('gpus_charts')
        .where('gpus_charts.gpu_model_id = :gpuModelId', { gpuModelId })
        .andWhere('gpus_charts.date >= :startDate', { startDate })
        .getMany();
    } catch (error) {
      this.logger.error(`Error in getGpuChartsData for gpuModelId: ${gpuModelId}: ${error}`);
      return [];
    }
  }

  private calculateGpuAverages(
    gpuChartsData: GpusCharts[],
    startDate: Date,
    period: IPeriod,
  ): {
    average: number;
    median: number;
  } {
    const rented: number[] = [];
    const days = periodToDays(period) - 1;

    for (let i = 0; i <= days; i++) {
      const date = addDays(startDate, i);
      const dataPoint = gpuChartsData.find((data) => format(data.date, GPU_CHART_DATA_FORMAT) === format(date, GPU_CHART_DATA_FORMAT));
      if (dataPoint) {
        rented.push(dataPoint.rented);
      } else {
        rented.push(0);
      }
    }

    return {
      average: getAverage(rented),
      median: getMedian(rented),
    };
  }

  private getStartDate(period: IPeriod, today: Date): Date {
    const days = periodToDays(period) - 1;
    return getTheStartOfDay(subDays(today, days));
  }

  private async getGpuHwPricing(): Promise<GpuHwPricing | undefined> {
    try {
      const pricing = await this.gpuHwPricingRepository.find();
      if (!pricing.length) {
        this.logger.error('No pricing found for gpuHwPricing');
        return undefined;
      }
      return pricing[0];
    } catch (error) {
      this.logger.error(`Error in getGpuHwPricing: ${error}`);
      return undefined;
    }
  }

  private calculateWeeklyHours(totalHours: number, period: IPeriod): number {
    const days = periodToDays(period);
    return parseFloat(((totalHours / days) * 7).toFixed(2));
  }

  private calculateWeeklyEarnings(totalEarned: number, period: IPeriod): number {
    const days = periodToDays(period);
    return parseFloat(((totalEarned / days) * 7).toFixed(2));
  }

  private async calculateUtilizationRate(gpuModel: IGpusModels, total_hours: number, startTime: Date, endTime: Date): Promise<number> {
    try {
      const { gpu_model, ram, cpu } = gpuModel;
      const gpuModelKey = `${gpu_model}-${ram}-${cpu}`;

      let totalGpuCount = this.gpuModelToGpuCountMap.get(gpuModelKey);
      if (totalGpuCount === undefined) {
        totalGpuCount = (await this.gpuHttpService.getGpuModelCount(gpu_model, cpu, ram)).count;
        this.gpuModelToGpuCountMap.set(gpuModelKey, totalGpuCount);
      }

      const hoursInPeriod = getHoursInDateRange(startTime, endTime);

      const totalAvailableHours = totalGpuCount * hoursInPeriod;

      let utilizationRate = 0;
      if (totalAvailableHours > 0) {
        utilizationRate = (total_hours / totalAvailableHours) * 100;
      }
      return convertToTwoDecimals(utilizationRate);
    } catch (error) {
      this.logger.error(`Error while calculating utilization rate for GPU model ${gpuModel.gpu_model}: ${error}`);
      return 0;
    }
  }

  private async calculateUserRetentionRate(gpuModelId: number): Promise<number> {
    const today = new Date();
    const startTime = this.getStartDate(IPeriod.MONTH, today);
    startTime.setHours(0, 0, 0, 0);

    const initialUsersBeforeStartTime = await this.gpusRentedRepository
      .createQueryBuilder('rented')
      .select('rented.user_id')
      .where('rented.gpu_model_id = :gpuModelId', { gpuModelId })
      .andWhere('rented.created_at <= :startTime', { startTime })
      .distinct(true)
      .getRawMany();

    const initialUsersBeforeStartTimeIds = initialUsersBeforeStartTime.map((user) => user.rented_user_id);

    if (initialUsersBeforeStartTimeIds.length === 0) {
      return 0;
    }

    const returningUsers = await this.gpusRentedRepository
      .createQueryBuilder('rented')
      .select('rented.user_id')
      .where('rented.gpu_model_id = :gpuModelId', { gpuModelId })
      .andWhere('rented.user_id IN (:...initialUsersBeforeStartTimeIds)', { initialUsersBeforeStartTimeIds })
      .andWhere('rented.created_at > :startTime OR rented.deallocated_at IS NULL OR rented.deallocated_at > :startTime', { startTime })
      .distinct(true)
      .getRawMany();

    const returningUserIds = returningUsers.map((user) => user.rented_user_id);

    const retentionRate = (returningUserIds.length / initialUsersBeforeStartTimeIds.length) * 100;

    return retentionRate;
  }

  private async getGpuChartsCumulativeStats(gpuModelId: number, startDate: Date): Promise<IGpuChartsCumulativeStats> {
    try {
      const result = await this.gpusChartsRepository
        .createQueryBuilder('gpus_charts')
        .select('COALESCE(SUM(gpus_charts.rented), 0)', 'total_rented')
        .addSelect('COALESCE(SUM(gpus_charts.earned), 0)', 'total_earned')
        .addSelect('COALESCE(SUM(gpus_charts.hours), 0)', 'total_hours')
        .where('gpus_charts.gpu_model_id = :gpuModelId', { gpuModelId })
        .andWhere('gpus_charts.date >= :startDate', { startDate })
        .getRawOne();

      return result || { total_rented: 0, total_earned: 0, total_hours: 0 };
    } catch (error) {
      this.logger.error(`Error in getGpuChartsCumulativeStats for gpuModelId: ${gpuModelId}: ${error}`);
      return { total_rented: 0, total_earned: 0, total_hours: 0 };
    }
  }
}
