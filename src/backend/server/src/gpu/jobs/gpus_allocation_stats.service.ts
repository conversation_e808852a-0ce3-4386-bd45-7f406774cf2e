import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { IGpuAllocationStatsQuery } from '@neural/models';
import { DataSource, Repository } from 'typeorm';
import { InstanceIdService } from '../../instance-id';
import { LeaderElectionService } from '../../leader-election';
import { GPU_ALLOCATION_STATS_JOB } from '../constants/jobs.constants';
import { GpuAllocationStats } from '../entities';

@Injectable()
export class GpuAllocationStatsService implements OnModuleInit {
  constructor(
    private readonly dataSource: DataSource,
    private readonly instanceIdService: InstanceIdService,
    private readonly leaderElectionService: LeaderElectionService,
    private readonly logger: Logger,
    @InjectRepository(GpuAllocationStats)
    readonly gpuAllocationStatsRepository: Repository<GpuAllocationStats>,
  ) {}

  async onModuleInit() {
    try {
      const atListOneAllocationStatsExists = await this.gpuAllocationStatsRepository.count();
      if (atListOneAllocationStatsExists > 0) {
        return;
      }
      await this.handleCron();
    } catch (error) {
      this.logger.error(`GpuAllocationStatsService onModuleInit error: ${error.message}`, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleCron() {
    if (process.env.NODE_ENV === 'development') return; // Disable cron for dev env.

    // Attempt to elect this instance as the leader
    const jobName = GPU_ALLOCATION_STATS_JOB;
    const instanceId = await this.instanceIdService.getInstanceId();
    const startTime = Date.now(); // Capture start time
    let renewLockInterval: NodeJS.Timeout | null = null;

    const isLeader = await this.leaderElectionService.electLeader(jobName, instanceId);
    if (!isLeader) {
      return; // Skip execution if this instance is not the leader
    }

    try {
      renewLockInterval = this.leaderElectionService.startLockRenewal(jobName, instanceId);

      const stats: IGpuAllocationStatsQuery[] = await this.dataSource.query(
        `SELECT
                hotkey,
                MAX(total_hours) AS longest_rental_hours,
                COUNT(*) AS total_allocation_count,
                COUNT(CASE WHEN deallocated_by = 'USER' OR deallocated_by = 'BALANCE_MONITOR' THEN 1 END) AS successful_deallocation_count
              FROM
                gpus_rented
              GROUP BY
                hotkey;`,
      );
      this.logger.log(`GpuAllocationStatsService handleCron: ${stats.length} stats found`);
      for (const stat of stats) {
        const reliability = stat.successful_deallocation_count / stat.total_allocation_count;

        await this.dataSource.query(
          `INSERT INTO gpus_allocation_stats (hotkey, total_allocation_count, longest_rental_hours, reliability)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (hotkey) DO UPDATE SET
              hotkey = EXCLUDED.hotkey,
              total_allocation_count = EXCLUDED.total_allocation_count,
              longest_rental_hours = EXCLUDED.longest_rental_hours,
              reliability = EXCLUDED.reliability;`,

          [stat.hotkey, stat.total_allocation_count, stat.longest_rental_hours, reliability],
        );
      }
      await this.leaderElectionService.handleJobSuccess(jobName, startTime, instanceId);
    } catch (error) {
      this.logger.error(`GpuAllocationStatsService handleCron error: ${error.message}`, error.stack);
      await this.leaderElectionService.handleDeadLetter(jobName, instanceId, error); // Move to DLQ
    } finally {
      await this.leaderElectionService.releaseLock(jobName, instanceId);
      if (renewLockInterval) {
        clearInterval(renewLockInterval);
      }
    }
  }
}
