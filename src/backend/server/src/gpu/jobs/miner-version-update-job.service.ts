import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { IGpuVersion, MAX_MINER_VERSION, MIN_MINER_VERSION } from '@neural/models';
import { EntityManager, Repository } from 'typeorm';
import { SupabaseService } from '../../core';
import { MINER_VERSION_UPDATE_JOB } from '../../gpu/constants/jobs.constants';
import { InstanceIdService } from '../../instance-id';
import { LeaderElectionService } from '../../leader-election';
import { GpusRented } from '../entities';
import { GpuHttpService } from '../http/gpu.http.service';
import { GpuService } from '../service/gpu.service';

@Injectable()
export class MinerVersionUpdateJobService {
  private readonly logger = new Logger(MinerVersionUpdateJobService.name);
  constructor(
    @InjectEntityManager()
    readonly entityManager: EntityManager,
    private readonly supabaseService: SupabaseService,
    private readonly instanceIdService: InstanceIdService,
    private readonly leaderElectionService: LeaderElectionService,
    private readonly gpuService: GpuService,
    private readonly gpuHttpService: GpuHttpService,
    @InjectRepository(GpusRented)
    readonly gpusRentedRepository: Repository<GpusRented>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async updateMinerVersion() {
    // Attempt to elect this instance as the leader
    const jobName = MINER_VERSION_UPDATE_JOB;
    const instanceId = await this.instanceIdService.getInstanceId();
    const startTime = Date.now(); // Capture start time
    let renewLockInterval: NodeJS.Timeout | null = null;

    const isLeader = await this.leaderElectionService.electLeader(jobName, instanceId);
    if (!isLeader) {
      return; // Skip execution if this instance is not the leader
    }
    try {
      // Start lock renewal process
      renewLockInterval = this.leaderElectionService.startLockRenewal(jobName, instanceId);

      this.logger.log('MinerVersionUpdateJobService cron job started');

      const rentedGpus = await this.gpuService.getRentedGpus();
      const hotkeys = rentedGpus.map((gpu) => gpu.hotkey);
      const minerVersion = (await this.gpuHttpService.checkMinerStatus(hotkeys, true)) as IGpuVersion[];
      // Map of hotkey to miner version for efficient lookup
      const minerVersionMap = new Map(minerVersion.map((data) => [data.hotkey, data.version]));

      const gpusToUpdate = rentedGpus
        .filter((gpu) => {
          const version = minerVersionMap.get(gpu.hotkey);
          if (!version) return false; // Exclude GPUs without version info

          // Log if the version is outside the allowed range
          if (version < MIN_MINER_VERSION || version > MAX_MINER_VERSION) {
            this.logger.log(`GPU with hotkey ${gpu.hotkey} has a miner version of ${version} (outside of 180-1000 range).`);
          }

          // Check if miner version has changed
          return gpu.miner_version !== version;
        })
        .map((gpu) => ({
          gpu_rented_id: gpu.gpu_rented_id,
          miner_version: minerVersionMap.get(gpu.hotkey) || null, // Get the version from the map
        }));

      for (const gpu of gpusToUpdate) {
        try {
          await this.gpusRentedRepository.update(gpu.gpu_rented_id, { miner_version: gpu.miner_version });
          this.logger.log(`Updated miner version for GPU with ID ${gpu.gpu_rented_id} to ${gpu.miner_version}`);
        } catch (updateError) {
          this.logger.error(`Failed to update miner version for GPU ${gpu.gpu_rented_id}: ${updateError}`);
        }
      }

      await this.leaderElectionService.handleJobSuccess(jobName, startTime, instanceId);
    } catch (error) {
      await this.leaderElectionService.handleDeadLetter(jobName, instanceId, error); // Move to DLQ
    } finally {
      await this.leaderElectionService.releaseLock(jobName, instanceId);
      if (renewLockInterval) {
        clearInterval(renewLockInterval);
      }
    }
  }
}
