import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, QueryRunner, Repository } from 'typeorm';
import { User } from '../../user/entities';
import { GpuCostHistory, GpusRented } from '../entities';

@Injectable()
export class CostService {
  constructor(
    @InjectRepository(GpuCostHistory)
    private readonly gpuCostHistoryRepository: Repository<GpuCostHistory>,
    @InjectRepository(GpusRented)
    readonly gpusRentedRepository: Repository<GpusRented>,
    private readonly logger: Logger,
  ) {}

  async createOrUpdateCost(user: User, total_hours: number, cost: number, dateIn: Date, queryRunner?: QueryRunner): Promise<void> {
    dateIn.setHours(0, 0, 0, 0);

    const gpuCostHistoryRepository = queryRunner ? queryRunner.manager.getRepository(GpuCostHistory) : this.gpuCostHistoryRepository;

    try {
      await gpuCostHistoryRepository.query(
        `
        INSERT INTO gpus_cost_history (user_id, date_cost_history, cost, total_hours)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (user_id, date_cost_history)
        DO UPDATE SET
          cost = gpus_cost_history.cost + EXCLUDED.cost,
          total_hours = gpus_cost_history.total_hours + EXCLUDED.total_hours;
        `,
        [user.user_id, dateIn, cost, total_hours],
      );
    } catch (error) {
      this.logger.error(`Failed to upsert cost history. User: ${user.user_id} for date: ${dateIn.toISOString()}. ${error.message}`);
      throw new InternalServerErrorException('Failed to upsert cost history');
    }
  }

  async getCurrentCost(user_id: string, queryRunner?: QueryRunner): Promise<number> {
    const repository = queryRunner ? queryRunner.manager.getRepository(GpusRented) : this.gpusRentedRepository;
    const GRACE_PERIOD_MINUTES = parseInt(process.env.GPU_PRICE_GRACE_PERIOD_MINUTES || '30', 10);

    const totalPrice = await repository
      .createQueryBuilder('gpus_rented')
      .select('SUM(gpus_rented.price)', 'total')
      .where('gpus_rented.user_id = :userId', { userId: user_id })
      .andWhere('gpus_rented.is_allocated = :isAllocated', { isAllocated: true })
      .andWhere('gpus_rented.is_payment_suspended = :isPaymentSuspended', { isPaymentSuspended: false })
      .andWhere(
        new Brackets((qb) => {
          qb.where('gpus_rented.online_status_received_at IS NULL').orWhere(
            `gpus_rented.online_status_received_at < NOW() - INTERVAL '${GRACE_PERIOD_MINUTES} minutes'`,
          );
        }),
      )
      .getRawOne();

    return totalPrice.total || 0;
  }
}
