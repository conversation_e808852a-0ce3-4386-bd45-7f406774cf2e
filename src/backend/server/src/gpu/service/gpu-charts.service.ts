import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  calculatePrice,
  getCategorySeries,
  getMedian,
  IPeriod,
  IChartAxisTypeEnum,
  IStackedColumnDatetimeSeries,
  periodToDays,
  ITopThreeGpuModelsChartsData,
  IRentedGpuState,
  IOtherGpuChartsData,
  GPU_CHART_DATA_FORMAT,
  IComparativeUtilizationStats,
  IGpusModels,
  getHoursInDateRange,
  convertToTwoDecimals,
  getStartAndEndOfDay,
  ITopThreeGpuModelsChartStats,
  IGpuModelChartStats,
  getTheStartOfDay,
  getTheEndOfDay,
} from '@neural/models';
import { addDays, format, subDays } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { Between, In, IsNull, <PERSON><PERSON><PERSON>, <PERSON>, QueryRunner, Repository } from 'typeorm';
import { GetUserHistoricalCostsDto } from '../dto';
import { GetGpuModelChartStatsDto } from '../dto/get-gpu-model-chart-stats.dto';
import { GpuHwPricing, GpusCharts, GpusPricing, GpusRented, GpusModels, GpuStats } from '../entities';
import { GpuHttpService } from '../http/gpu.http.service';

@Injectable()
export class GpuChartsService {
  constructor(
    private readonly logger: Logger,
    @InjectRepository(GpusCharts)
    readonly gpusChartsRepository: Repository<GpusCharts>,
    @InjectRepository(GpusPricing)
    readonly gpusPricingRepository: Repository<GpusPricing>,
    @InjectRepository(GpuHwPricing)
    readonly gpuHwPricingRepository: Repository<GpuHwPricing>,
    private readonly gpuHttpService: GpuHttpService,
    @InjectRepository(GpusRented)
    readonly gpusRentedRepository: Repository<GpusRented>,
    @InjectRepository(GpusModels)
    readonly gpusModelsRepository: Repository<GpusModels>,
    @InjectRepository(GpuStats)
    readonly gpuStatsRepository: Repository<GpuStats>,
  ) {}

  async addEarnedAndHoursToGpuCharts(gpuModelId: number, earned: number, hours: number, queryRunner?: QueryRunner) {
    try {
      const today = toZonedTime(new Date(format(new Date(), GPU_CHART_DATA_FORMAT)), 'UTC');

      const { rented, available } = await this.getOrCreateGpuChart(gpuModelId, today, false);

      const repository = queryRunner ? queryRunner.manager : this.gpusChartsRepository;

      const result = await repository.query(
        `INSERT INTO gpus_charts (date, rented, available, hours, earned, gpu_model_id)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (date, gpu_model_id)
        DO UPDATE SET
          earned = gpus_charts.earned + EXCLUDED.earned,
          hours = gpus_charts.hours + EXCLUDED.hours
        RETURNING gpus_chart_id, gpu_model_id;`,
        [today, rented, available, hours, earned, gpuModelId],
      );

      this.logger.log(
        `Added earned and hours to gpu chart for gpuModelId: ${result[0]?.gpu_model_id} with gpus_chart_id: ${result[0]?.gpus_chart_id}`,
      );
    } catch (error) {
      this.logger.error(`Error in addEarnedAndHoursToGpuCharts for gpuModelId: ${gpuModelId}: ${error.message}`, error);
    }
  }

  async addRentedToGpuCharts(gpuModelId: number, queryRunner?: QueryRunner) {
    try {
      const today = toZonedTime(new Date(format(new Date(), GPU_CHART_DATA_FORMAT)), 'UTC');

      const gpuChart = await this.getOrCreateGpuChart(gpuModelId, today, true);

      if (queryRunner) {
        await queryRunner.manager.save(gpuChart);
      } else {
        await this.gpusChartsRepository.save(gpuChart);
      }
    } catch (error) {
      this.logger.error(`Error in addRentedToGpuCharts for gpuModelId: ${gpuModelId}: ${error.message}`);
    }
  }

  private async getTopThreeGpuModelsIds(): Promise<number[]> {
    try {
      const topThreeStats = await this.gpuStatsRepository.find({
        order: { rentals_7d: 'DESC' },
        take: 3,
      });
      return topThreeStats.map((gpuStats) => gpuStats.gpu_model_id);
    } catch (error) {
      this.logger.error(`Error in getTopThreeGpuModelsIds: ${error.message}`);
      throw new InternalServerErrorException('Error while fetching top 3 GPU models');
    }
  }

  async getTopThreeGpuChartsWithSeries(query: GetUserHistoricalCostsDto): Promise<ITopThreeGpuModelsChartStats[]> {
    try {
      const { period } = query;
      const today = new Date();
      const startDate = getTheStartOfDay(this.getStartDate(period, today));
      const topThreeStats = await this.gpuStatsRepository.find({
        where: { rentals_7d: Not(IsNull()) },
        order: { rentals_7d: 'DESC' },
        take: 3,
      });

      const gpusHwPricing = await this.getGpuHwPricing();
      const results = await Promise.all(
        topThreeStats.map(async (gpuStats) => {
          const gpuModel = await this.gpusModelsRepository.findOne({
            where: { gpus_model_id: gpuStats.gpu_model_id },
            relations: ['gpuPricing'],
          });
          if (!gpuModel || !gpuModel.gpuPricing) return null;

          const gpuChartsData = await this.getGpuChartsData(gpuStats.gpu_model_id, startDate, today);
          const { rentedGpus, totalAvailableGpus } = this.processGpuChartsData(gpuChartsData, startDate, period);
          const series = [rentedGpus, totalAvailableGpus];
          const serie = period === IPeriod.WEEK ? getCategorySeries(series) : series;

          return {
            gpuModel: gpuModel,
            gpuModelVram: gpuModel.ram,
            price: calculatePrice(gpusHwPricing[0], gpuModel.gpuPricing.price, gpuModel.ram, gpuModel.cpu),
            activeSince: gpuModel.created_at,
            gpuChartStats: gpuStats,
            series: serie,
            period,
            type: period === IPeriod.WEEK ? IChartAxisTypeEnum.CATEGORY : IChartAxisTypeEnum.DATETIME,
            serieLength: serie[0].data.length,
            days: periodToDays(period),
          };
        }),
      );

      return results.filter((result) => result !== null);
    } catch (error) {
      this.logger.error(`Error in getTopThreeGpuChartsWithSeries: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Error while fetching top 3 GPU charts data');
    }
  }

  async getGpuModelChartStats(query: GetGpuModelChartStatsDto): Promise<IGpuModelChartStats> {
    try {
      const { period, gpuModelId } = query;
      const today = new Date();
      const startDate = getTheStartOfDay(this.getStartDate(period, today));
      const gpuModel = await this.gpusModelsRepository.findOne({ where: { gpus_model_id: gpuModelId } });
      if (!gpuModel) {
        throw new Error(`GPU model not found for gpuModelId: ${gpuModelId}`);
      }

      const gpuChartsData = await this.getGpuChartsData(gpuModelId, startDate, today);
      const { rentedGpus, totalAvailableGpus } = this.processGpuChartsData(gpuChartsData, startDate, period);
      const series = [rentedGpus, totalAvailableGpus];
      return {
        series: period === IPeriod.WEEK ? getCategorySeries(series) : series,
        period,
        type: period === IPeriod.WEEK ? IChartAxisTypeEnum.CATEGORY : IChartAxisTypeEnum.DATETIME,
        serieLength: series[0].data.length,
        days: periodToDays(period),
      };
    } catch (error) {
      this.logger.error(`Error in getGpuModelChartStats: ${error.message}`);
      throw new InternalServerErrorException('Error while fetching GPU model chart stats');
    }
  }

  private getStartDate(period: IPeriod, today: Date): Date {
    const days = periodToDays(period) - 1;
    return subDays(today, days);
  }

  private async getGpuHwPricing(): Promise<GpuHwPricing[]> {
    const pricing = await this.gpuHwPricingRepository.find();
    if (!pricing.length) {
      this.logger.error('No pricing found for gpuHwPricing');
      throw new InternalServerErrorException('No pricing found for gpuHwPricing');
    }
    return pricing;
  }

  private async getGpuChartsData(gpuModelId: number, startDate: Date, endDate: Date): Promise<GpusCharts[]> {
    try {
      return await this.gpusChartsRepository
        .createQueryBuilder('gpus_charts')
        .where('gpus_charts.gpu_model_id = :gpuModelId', { gpuModelId })
        .andWhere('gpus_charts.date BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere('gpus_charts.rented IS NOT NULL AND gpus_charts.available IS NOT NULL')
        .getMany();
    } catch (error) {
      this.logger.error(`Error in getGpuChartsData for gpuModelId: ${gpuModelId}: ${error}`);
      throw new InternalServerErrorException('Error while fetching GPU charts data');
    }
  }

  private processGpuChartsData(gpuChartsData: GpusCharts[], startDate: Date, period: IPeriod) {
    const rentedGpus: IStackedColumnDatetimeSeries = { name: 'Rented GPUs', data: [] };
    const totalAvailableGpus: IStackedColumnDatetimeSeries = { name: 'Total Available GPUs', data: [] };
    const days = periodToDays(period) - 1;
    for (let i = 0; i <= days; i++) {
      const date = addDays(startDate, i);
      const dataPoint = gpuChartsData.find((data) => format(data.date, GPU_CHART_DATA_FORMAT) === format(date, GPU_CHART_DATA_FORMAT));

      rentedGpus.data.push([date.getTime(), dataPoint?.rented ?? 0]);
      totalAvailableGpus.data.push([date.getTime(), dataPoint?.available ?? 0]);
    }
    return { rentedGpus, totalAvailableGpus };
  }

  async getOtherGpusChartData(query: GetUserHistoricalCostsDto): Promise<IRentedGpuState> {
    try {
      const { period } = query;
      const today = format(new Date(), GPU_CHART_DATA_FORMAT);
      const days = periodToDays(period) - 1;
      const startDate = getTheStartOfDay(subDays(today, days));
      const endDate = getTheEndOfDay(new Date(today));

      const otherGpus = await this.getOtherGpus(startDate, new Date(endDate));

      const rentedGpus: IStackedColumnDatetimeSeries = {
        name: 'Rented GPUs',
        data: [],
      };

      const totalAvailableGpus: IStackedColumnDatetimeSeries = {
        name: 'Total Available GPUs',
        data: [],
      };

      let maxTotalRentedGpus = 0;

      for (let i = 0; i < days + 1; i++) {
        const date = addDays(startDate, i);
        const dataPoint = otherGpus.find((data) => format(data.date, GPU_CHART_DATA_FORMAT) === format(date, GPU_CHART_DATA_FORMAT));
        rentedGpus.data.push([new Date(date).getTime(), dataPoint?.total_rented ?? 0]);
        totalAvailableGpus.data.push([new Date(date).getTime(), dataPoint?.total_available ?? 0]);
        maxTotalRentedGpus = Math.max(maxTotalRentedGpus, dataPoint?.total_rented ?? 0);
      }

      const serie = [rentedGpus, totalAvailableGpus];
      return {
        serie: period === IPeriod.WEEK ? getCategorySeries(serie) : serie,
        period,
        max: maxTotalRentedGpus.toString(),
        median: getMedian(rentedGpus.data.map((data) => data[1])).toString(),
        type: period === IPeriod.WEEK ? IChartAxisTypeEnum.CATEGORY : IChartAxisTypeEnum.DATETIME,
      };
    } catch (error) {
      throw new InternalServerErrorException('Error while fetching other GPU charts data');
    }
  }

  private async getOtherGpus(startDate: Date, endDate: Date): Promise<IOtherGpuChartsData[]> {
    const topThreeGpuModelsIds = await this.getTopThreeGpuModelsIds();
    return await this.gpusChartsRepository
      .createQueryBuilder('gpus_charts')
      .select('gpus_charts.date', 'date')
      .addSelect('COALESCE(SUM(gpus_charts.available), 0)', 'total_available')
      .addSelect('COALESCE(SUM(gpus_charts.rented), 0)', 'total_rented')
      .andWhere('gpus_charts.date BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('gpus_charts.gpu_model_id NOT IN (:...topThreeGpuModelsIds)', { topThreeGpuModelsIds })
      .groupBy('gpus_charts.date') // Group by date
      .getRawMany();
  }

  async getTotalRentedGpuChartData(query: GetUserHistoricalCostsDto): Promise<IRentedGpuState> {
    try {
      const { period } = query;
      const today = format(new Date(), GPU_CHART_DATA_FORMAT);
      const days = periodToDays(period) - 1;
      const startDate = getTheStartOfDay(subDays(today, days));
      const endDate = getTheEndOfDay(new Date(today));

      const rentedGpus: IStackedColumnDatetimeSeries = {
        name: 'Rented GPUs',
        data: [],
      };

      const totalAvailableGpus: IStackedColumnDatetimeSeries = {
        name: 'Total Available GPUs',
        data: [],
      };

      let maxTotalRentedGpus = 0;

      const totalGpuChartsData = await this.getTotalGpus(startDate, new Date(endDate));

      for (let i = 0; i < days + 1; i++) {
        const date = addDays(startDate, i);
        const dataPoint = totalGpuChartsData.find(
          (data) => format(data.date, GPU_CHART_DATA_FORMAT) === format(date, GPU_CHART_DATA_FORMAT),
        );
        rentedGpus.data.push([new Date(date).getTime(), dataPoint?.total_rented ?? 0]);
        totalAvailableGpus.data.push([new Date(date).getTime(), dataPoint?.total_available ?? 0]);
        maxTotalRentedGpus = Math.max(maxTotalRentedGpus, dataPoint?.total_rented ?? 0);
      }

      const serie = [rentedGpus, totalAvailableGpus];
      return {
        serie: period === IPeriod.WEEK ? getCategorySeries(serie) : serie,
        period,
        max: maxTotalRentedGpus.toString(),
        median: getMedian(rentedGpus.data.map((data) => data[1])).toString(),
        type: period === IPeriod.WEEK ? IChartAxisTypeEnum.CATEGORY : IChartAxisTypeEnum.DATETIME,
      };
    } catch (error) {
      this.logger.error(`Error in getTotalRentedGpuChartData: ${error}`);
      throw new InternalServerErrorException('Error while fetching total rented GPU charts data');
    }
  }

  private async getTotalGpus(startDate: Date, endDate: Date): Promise<IOtherGpuChartsData[]> {
    try {
      return await this.gpusChartsRepository
        .createQueryBuilder('gpus_charts')
        .select('gpus_charts.date', 'date')
        .addSelect('COALESCE(SUM(gpus_charts.available), 0)', 'total_available')
        .addSelect('COALESCE(SUM(gpus_charts.rented), 0)', 'total_rented')
        .andWhere('gpus_charts.date BETWEEN :startDate AND :endDate', { startDate, endDate })
        .groupBy('gpus_charts.date') // Group by date
        .getRawMany();
    } catch (error) {
      this.logger.error(`Error while fetching total rented GPU charts data: ${error}`);
      throw new InternalServerErrorException('Error while fetching total rented GPU charts data');
    }
  }

  async getComparativeUtilizationStatsData(): Promise<IComparativeUtilizationStats[]> {
    return await this.gpusRentedRepository
      .createQueryBuilder('gpus_rented')
      .select('gpus_rented.gpu_model_id', 'gpu_model_id') // Select the GPU model ID
      .addSelect('COUNT(gpus_rented.gpu_model_id)', 'gpu_rented_count') // Count how many times each GPU model has been rented
      .addSelect('ROW_NUMBER() OVER (ORDER BY COUNT(gpus_rented.gpu_model_id) DESC)', 'rank') // Rank the GPU models by rented count
      .where('gpus_rented.is_allocated = :is_allocated', { is_allocated: true }) // Filter to only include where is_allocated = false
      .groupBy('gpus_rented.gpu_model_id') // Group by GPU model ID to aggregate the counts
      .orderBy('COUNT(gpus_rented.gpu_model_id)', 'DESC') // Order by the rental count in descending order
      .addSelect(
        `
        CASE
          WHEN ROW_NUMBER() OVER (ORDER BY COUNT(gpus_rented.gpu_model_id) DESC) = 1 THEN 'top_1'
          WHEN ROW_NUMBER() OVER (ORDER BY COUNT(gpus_rented.gpu_model_id) DESC) = 2 THEN 'top_2'
          WHEN ROW_NUMBER() OVER (ORDER BY COUNT(gpus_rented.gpu_model_id) DESC) = 3 THEN 'top_3'
          ELSE 'rest_all'
        END`,
        'category',
      ) // Add a column for ranking the GPUs into top_1, top_2, top_3, and rest_all
      .getRawMany(); // Execute the query and return raw results
  }

  async getComparativeUtilizationStats() {
    try {
      // Get GPU stats from the service
      const { data: { stats: { available = 0 } = {} } = {} } = await this.gpuHttpService.getGpuStats();

      // Fetch comparative utilization data
      const comparativeUtilizationStats = await this.getComparativeUtilizationStatsData();

      // Initialize result object with categories, all defaulting to 0
      const result = {
        top_1: 0,
        top_2: 0,
        top_3: 0,
        rest_all: 0, // rest_all will store the sum of all 'rest_all' counts
        total: 0,
        available,
      };

      // Loop through the data and populate the result object
      if (Array.isArray(comparativeUtilizationStats)) {
        comparativeUtilizationStats.forEach((item) => {
          if (item && typeof item === 'object') {
            const { category, gpu_rented_count } = item;

            // Ensure gpu_rented_count is valid before parsing
            const parsedCount = parseInt(gpu_rented_count, 10) || 0;

            // Handle categories and safely add to result object
            if (category === 'rest_all') {
              result.rest_all += parsedCount; // Sum up rest_all counts
            } else if (['top_1', 'top_2', 'top_3'].includes(category)) {
              result[category] = parsedCount; // Assign to top_1, top_2, or top_3
            }
          }
        });
      }

      // Calculate the total sum of top_1, top_2, top_3, rest_all, and available
      result.total = result.top_1 + result.top_2 + result.top_3 + result.rest_all + result.available;

      // Assign the pre-calculated offsetX values in chartOptions
      const series = [
        {
          name: '3', // Third dataset (Third Most Rented GPUs)
          data: [(result.top_3 / result.total) * 100],
        },
        {
          name: '2', // Second dataset (Second Most Rented GPUs)
          data: [(result.top_2 / result.total) * 100],
        },
        {
          name: '1', // First dataset (First Most Rented GPUs)
          data: [(result.top_1 / result.total) * 100],
        },
        {
          name: 'Rest Rented', // Fourth dataset (Rest of Rented GPUs)
          data: [(result.rest_all / result.total) * 100],
        },
        {
          name: 'Unrented', // Unrented GPUs
          data: [(result.available / result.total) * 100],
        },
      ];

      return {
        series,
        ...result,
      };
    } catch (e) {
      this.logger.error('Error fetching comparative utilization stats:', e);
      // You could return a fallback result or propagate the error as needed
      return {
        top_1: 0,
        top_2: 0,
        top_3: 0,
        rest_all: 0,
        total: 0,
        available: 0,
        series: [
          {
            name: '3', // Third dataset (Third Most Rented GPUs)
            data: [0],
          },
          {
            name: '2', // Second dataset (Second Most Rented GPUs)
            data: [0],
          },
          {
            name: '1', // First dataset (First Most Rented GPUs)
            data: [0],
          },
          {
            name: 'Rest Rented', // Fourth dataset (Rest of Rented GPUs)
            data: [0],
          },
          {
            name: 'Unrented', // Unrented GPUs
            data: [0],
          },
        ],
      };
    }
  }

  private async getOrCreateGpuChart(gpuModelId: number, today: Date, isRental: boolean): Promise<GpusCharts> {
    const gpuChart = await this.gpusChartsRepository.findOne({
      where: {
        gpu_model_id: gpuModelId,
        date: today,
      },
    });

    if (!gpuChart) {
      const { available, rented } = await this.calculateAvailability(gpuModelId, today);
      const newGpuChart = new GpusCharts();
      newGpuChart.date = today;
      newGpuChart.gpu_model_id = gpuModelId;
      newGpuChart.available = available;
      newGpuChart.rented = rented;
      return newGpuChart;
    }
    if (isRental) {
      gpuChart.rented = (gpuChart.rented || 0) + 1;
    }
    return gpuChart;
  }

  private async calculateAvailability(gpuModelId: number, today: Date, queryRunner?: QueryRunner) {
    try {
      const { startDate, endDate } = getStartAndEndOfDay(today);

      const gpuModel = queryRunner
        ? await queryRunner.manager.findOne(GpusModels, { where: { gpus_model_id: gpuModelId } })
        : await this.gpusModelsRepository.findOne({ where: { gpus_model_id: gpuModelId } });

      if (!gpuModel) {
        throw new Error(`GPU model not found for gpuModelId: ${gpuModelId}`);
      }

      const gpuCount = (await this.gpuHttpService.getGpuModelCount(gpuModel.gpu_model, gpuModel.cpu, gpuModel.ram)).count;

      const [activeRentalBeforeToday, todayRentalsCount] = await Promise.all([
        this.gpusRentedRepository.count({
          where: { gpu_model_id: gpuModelId, is_allocated: true, created_at: LessThan(startDate) },
        }),
        this.gpusRentedRepository.count({
          where: { gpu_model_id: gpuModelId, created_at: Between(startDate, endDate) },
        }),
      ]);

      const available = gpuCount - activeRentalBeforeToday;
      return { available: available > 0 ? available : 1, rented: todayRentalsCount };
    } catch (error) {
      this.logger.error(`Error in calculateAvailability for gpuModelId: ${gpuModelId}: ${error.message}`);
      return { available: 1, rented: 1 };
    }
  }
}
