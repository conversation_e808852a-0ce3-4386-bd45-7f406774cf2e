import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  IBalanceMonitorConfig,
  NotificationType,
  generateUserLowBalanceCacheKey,
  USER_LOW_BALANCE_CACHE_TTL,
  BalanceMonitorResultDto,
} from '@neural/models';
import { Cache } from 'cache-manager';
import { Repository, DataSource } from 'typeorm';
import { GpuUtilityService } from '../../gpu-utility/services/gpu-utility/gpu-utility.service';
import { NotificationGateway, NotifyService } from '../../notification';
import { PaymentService } from '../../payment/services/payment.service';
import { GpusRented } from '../entities';
import { BALANCE_MONITOR_CONFIG } from '../gpu.token';
import { GpuService } from './gpu.service';

@Injectable()
export class GpuCostMonitorService {
  private readonly logger = new Logger(GpuCostMonitorService.name);

  constructor(
    @InjectRepository(GpusRented)
    private readonly gpusRentedRepository: Repository<GpusRented>,
    @Inject(BALANCE_MONITOR_CONFIG)
    private readonly balanceMonitorConfig: IBalanceMonitorConfig,
    private readonly notificationGateway: NotificationGateway,
    private readonly notifyService: NotifyService,
    private readonly paymentService: PaymentService,
    private readonly gpuService: GpuService,
    private readonly gpuUtilityService: GpuUtilityService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly dataSource: DataSource,
  ) {}

  async monitorAndTopUpBalances(): Promise<void> {
    try {
      this.logger.log(`Monitoring and topping up balances started on ${new Date().toISOString()}`);
      const result: BalanceMonitorResultDto[] = await this.dataSource.query(`
        SELECT *
        FROM monitor_and_update_balances();
      `);

      for (const {
        user_id_out: userId,
        current_cost_out: currentCost,
        balance_out: balance,
        auto_topup_active_out: autoTopUpActive,
        auto_topup_amount_out: autoTopUpAmount,
        auto_topup_threshold_out: autoTopUpThreshold,
      } of result) {
        this.notificationGateway.updateBalance(userId, balance);
        this.notificationGateway.updateCurrentCost(userId, currentCost);
        this.logger.log(`updateBalance event emitted`);

        const userCurrentRunway = await this.gpuUtilityService.calculateCurrentRunwayTime(userId);
        this.notificationGateway.updateCurrentRunway(userId, userCurrentRunway);

        let expectedUserBalance = balance;

        const conditionForTopUp =
          autoTopUpActive && autoTopUpAmount && autoTopUpThreshold && balance <= Math.floor(autoTopUpThreshold * 100);
        if (conditionForTopUp) {
          const autoTopUpSuccess = await this.paymentService.performAutoTopUp(userId, Math.ceil(autoTopUpAmount * 100));
          if (autoTopUpSuccess) {
            expectedUserBalance += Math.ceil(autoTopUpAmount * 100);
          }
        } else {
          if (autoTopUpActive) {
            this.logger.log(
              `Auto top did not triggerd for user ` +
                `for amount '${autoTopUpAmount}' ` +
                `when auto_topup_amount is ${autoTopUpAmount}, ` +
                `auto_topup_threshold is ${autoTopUpThreshold} ` +
                `balance is ${balance} ` +
                `on ${new Date().toISOString()}`,
            );
          }
        }

        // zer balance  deallocation and low balance notification  will be handled inhere(this will not be done in the database function)
        const balanceMonitorIntervalInHours = this.balanceMonitorConfig.balanceMonitorIntervalInMinutes / 60;
        const nextIntervalCostInCents = parseFloat((currentCost * 100 * balanceMonitorIntervalInHours).toFixed(1));
        let userHasGpu = true;
        if (nextIntervalCostInCents > expectedUserBalance) {
          await this.deallocateGpu(userId);
          userHasGpu = false;
          const lowBalanceNotification = autoTopUpActive
            ? `To reduce the frequency of this notification, please increase your auto top up amount or top up your account manually.`
            : 'To reduce the frequency of this notification, please top up your account manually or enable auto top up with enough amount.';
          await this.notifyService.notifyUser(
            userId,
            NotificationType.ZERO_BALANCE,
            `Sorry, your balance is very low. Please top up your account to continue using the service. ${lowBalanceNotification}`,
          );
          this.logger.log(`All Gpu's deallocated for user due to low balance
            on ${new Date().toISOString()}`);
        }

        // Notify user if balance is running low
        const balanceBufferTimeInHours = this.balanceMonitorConfig.balanceBufferTimeInMinutes / 60;
        const bufferTimeCostInCents = parseFloat((currentCost * 100 * balanceBufferTimeInHours).toFixed(1));
        const lowBalanceNotificationSent = await this.cacheManager.get<string>(generateUserLowBalanceCacheKey(userId));
        if (balance < bufferTimeCostInCents && userHasGpu && !lowBalanceNotificationSent) {
          const lowBalanceNotification = autoTopUpActive
            ? `To avoid losing your GPU resources, please increase your auto top up amount or top up your account manually.`
            : 'To avoid losing your GPU resources, please top up your account manually or enable auto top up with enough amount.';
          await this.notifyService.notifyUser(
            userId,
            NotificationType.LOW_BALANCE,
            `Your balance is running low. Your GPU resources will be deallocated in the next ${balanceBufferTimeInHours} hour(s). ${lowBalanceNotification}`,
          );
          await this.cacheManager.set(generateUserLowBalanceCacheKey(userId), userId, USER_LOW_BALANCE_CACHE_TTL);
        }
      }
      this.logger.log(
        `Monitoring and topping up balances completed and updateBalance event emitted successfuly on ${new Date().toISOString()}`,
      );
    } catch (error) {
      this.logger.error(`Error monitoring and topping up balances on ${new Date().toISOString()}`, error);
    }
  }

  private async deallocateGpu(user_id: string): Promise<void> {
    try {
      const rentals = await this.gpusRentedRepository.find({ where: { user_id: user_id, is_allocated: true } });
      for (const rental of rentals) {
        try {
          await this.gpuService.deallocateGpuDueToLowBalance(user_id, rental);
        } catch (error) {
          this.logger.error('Zero Balance: Error deallocating gpu with gpu_rented_id', rental.gpu_rented_id, error);
        }
      }
    } catch (error) {
      this.logger.error('Error deallocating gpu', error);
    }
  }
}
