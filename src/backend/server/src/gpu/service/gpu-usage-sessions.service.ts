import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { convertCostToCents, IGpuPaymentStatus } from '@neural/models';
import { IsNull, Repository, QueryRunner } from 'typeorm';
import { GpusUsageSessions } from '../entities/gpus-usage-sessions.entity';

@Injectable()
export class GpuUsageSessionsService {
  constructor(
    private readonly logger: Logger,
    @InjectRepository(GpusUsageSessions)
    private readonly gpusUsageSessionsRepository: Repository<GpusUsageSessions>,
  ) {}

  async createNewGpuUsageOnRent(gpuRentedId: string, start_date: Date, queryRunner?: QueryRunner): Promise<void> {
    try {
      await this.createNewGpusUsage(gpuRentedId, IGpuPaymentStatus.ONLINE, start_date, 0, queryRunner);
    } catch (error) {
      this.logger.error(`Error while creating new gpu usage on rent`, error, {
        gpu_rented_id: gpuRentedId,
      });
    }
  }

  async endGpuUsageOnDeallocation(gpuRentedId: string, end_date: Date, addedCost: number, queryRunner?: QueryRunner): Promise<void> {
    try {
      const currentSession = queryRunner
        ? await queryRunner.manager.findOne(GpusUsageSessions, { where: { gpu_rented_id: gpuRentedId, end_date: IsNull() } })
        : await this.gpusUsageSessionsRepository.findOne({ where: { gpu_rented_id: gpuRentedId, end_date: IsNull() } });

      if (currentSession) {
        currentSession.end_date = end_date;
        currentSession.cost += convertCostToCents(addedCost);
        queryRunner
          ? await queryRunner.manager.update(GpusUsageSessions, currentSession.gpu_usage_session_id, {
              end_date: end_date,
              cost: currentSession.cost,
            })
          : await this.gpusUsageSessionsRepository.update(currentSession.gpu_usage_session_id, {
              end_date: end_date,
              cost: currentSession.cost,
            });
      }
    } catch (error) {
      this.logger.error(`Error while ending gpu usage on deallocation`, error, {
        gpu_rented_id: gpuRentedId,
      });
    }
  }

  async updateGpusUsage(
    gpuRentedId: string,
    newState: IGpuPaymentStatus,
    date: Date,
    addedCost: number,
    queryRunner?: QueryRunner,
  ): Promise<void> {
    try {
      const currentSession = queryRunner
        ? await queryRunner.manager.findOne(GpusUsageSessions, { where: { gpu_rented_id: gpuRentedId, end_date: IsNull() } })
        : await this.gpusUsageSessionsRepository.findOne({ where: { gpu_rented_id: gpuRentedId, end_date: IsNull() } });

      if (currentSession && currentSession.state === newState) {
        currentSession.cost += convertCostToCents(addedCost);
        queryRunner
          ? await queryRunner.manager.update(GpusUsageSessions, currentSession.gpu_usage_session_id, { cost: currentSession.cost })
          : await this.gpusUsageSessionsRepository.update(currentSession.gpu_usage_session_id, { cost: currentSession.cost });
      } else if (currentSession && currentSession.state !== newState) {
        currentSession.end_date = date;
        queryRunner
          ? await queryRunner.manager.update(GpusUsageSessions, currentSession.gpu_usage_session_id, { end_date: date })
          : await this.gpusUsageSessionsRepository.update(currentSession.gpu_usage_session_id, { end_date: date });

        await this.createNewGpusUsage(gpuRentedId, newState, date, addedCost, queryRunner);
      } else {
        await this.createNewGpusUsage(gpuRentedId, newState, date, addedCost, queryRunner);
      }
    } catch (error) {
      this.logger.error(`Error while updating gpu usage`, error, {
        gpu_rented_id: gpuRentedId,
      });
    }
  }

  private async createNewGpusUsage(
    gpuRentedId: string,
    newState: IGpuPaymentStatus,
    date: Date,
    initialCost: number,
    queryRunner?: QueryRunner,
  ): Promise<GpusUsageSessions | null> {
    try {
      const newSession = this.gpusUsageSessionsRepository.create({
        gpu_rented_id: gpuRentedId,
        state: newState,
        start_date: date,
        cost: convertCostToCents(initialCost),
      });
      return queryRunner ? await queryRunner.manager.save(newSession) : await this.gpusUsageSessionsRepository.save(newSession);
    } catch (error) {
      this.logger.error(`Error while creating new gpu usage`, error, {
        gpu_rented_id: gpuRentedId,
      });
      return null;
    }
  }
}
