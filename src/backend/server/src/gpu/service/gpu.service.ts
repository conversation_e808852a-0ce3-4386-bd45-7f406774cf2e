import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  Logger,
  HttpException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  IAverageCost,
  IBalanceMonitorConfig,
  IPeriod,
  ICostState,
  IDefaultGpusHowPricing,
  IGpuFlat,
  IGpuResourceSearch,
  IRentedGpuSearch,
  NotificationType,
  UserRole,
  calculateGpuPrice,
  constructRentedGpuModelName,
  convertBytesToGB,
  convertMbToGB,
  convertMinerDataToArray,
  generateSerie,
  getGpuPricing,
  roundToNearestCent,
  IResourceUsage,
  GpuDeallocateByEnum,
  costBasedOnMinutes,
  convertCostToCents,
  IAvailableGpuResponseData,
  IGpusRented,
  IRentedListPagination,
  isValidMinerData,
  IGpuStatus,
  IMinerSpecsData,
  roundUpToWhole,
  MinerAvailableText,
  ONLINE,
  OFFLINE,
  costBasedOnSeconds,
  secondsToHours,
  getTheEndOfDay,
  getTheStartOfDay,
  periodToDays,
  generateGpuBeingAllocatedCacheKey,
  IUserEntity,
  SearchGpuData,
  IGpuAllocationStats,
  IGpuPricesSearcher,
  IMinerStatus,
  MIN_MINER_VERSION,
} from '@neural/models';
import { Cache } from 'cache-manager';
import { addMinutes, addSeconds, differenceInSeconds, isAfter, isBefore, subDays, subMonths, subQuarters, subYears } from 'date-fns';
import { And, LessThanOrEqual, MoreThanOrEqual, QueryRunner, Repository } from 'typeorm';
import { EarningService } from '../../earning/service/earning.service';
import { EmailJobService } from '../../email/email-job.service';
import { GpuUtilityService } from '../../gpu-utility/services/gpu-utility/gpu-utility.service';
import { NotificationGateway, NotifyService } from '../../notification';
import { PaymentService } from '../../payment/services/payment.service';
import { SshKey } from '../../user/entities/ssh-key.entity';
import { User } from '../../user/entities/user.entity';
import { DeployGpuDto, GetMinerVersionDto, UpdateRentedGpuDto } from '../dto';
import { UpdateGpuPricesDto } from '../dto/update-gpu-price.dto';
import {
  GpuCostHistory,
  GpuDailyCount,
  GpuHwPricing,
  GpusPricing,
  GpusRented,
  GpusModels,
  GpuAllocationStats,
  GpuDeploymentFailure,
} from '../entities';
import { BALANCE_MONITOR_CONFIG, DEFAULT_GPUS_HW_PRICING } from '../gpu.token';
import { GpuHttpService } from '../http/gpu.http.service';
import { CostService } from './cost.service';
import { GpuChartsService } from './gpu-charts.service';
import { GpuUsageSessionsService } from './gpu-usage-sessions.service';

@Injectable()
export class GpuService {
  private readonly logger = new Logger(GpuService.name);
  constructor(
    @Inject(DEFAULT_GPUS_HW_PRICING)
    private readonly defaultGpusHowPricing: IDefaultGpusHowPricing,
    @Inject(BALANCE_MONITOR_CONFIG)
    private readonly balanceMonitorConfig: IBalanceMonitorConfig,
    @InjectRepository(GpusRented)
    readonly gpusRentedRepository: Repository<GpusRented>,
    @InjectRepository(GpusPricing)
    readonly gpusPricingRepository: Repository<GpusPricing>,
    @InjectRepository(GpuHwPricing)
    readonly gpuHwPricingRepository: Repository<GpuHwPricing>,
    @InjectRepository(SshKey)
    readonly sshKeyRepository: Repository<SshKey>,
    @InjectRepository(User)
    readonly userRepository: Repository<User>,
    @InjectRepository(GpuCostHistory)
    private readonly gpuCostHistoryRepository: Repository<GpuCostHistory>,
    @InjectRepository(GpuDailyCount)
    readonly gpuDailyCountRepository: Repository<GpuDailyCount>,
    @InjectRepository(GpusModels)
    readonly gpusModelsRepository: Repository<GpusModels>,
    private readonly notifyService: NotifyService,
    private readonly GpuHttpService: GpuHttpService,
    private readonly costService: CostService,
    private readonly earningService: EarningService,
    private readonly notificationGateway: NotificationGateway,
    private readonly paymentService: PaymentService,
    private readonly gpuChartsService: GpuChartsService,
    private readonly gpuUsageSessionsService: GpuUsageSessionsService,
    private readonly gpuUtilityService: GpuUtilityService,
    private readonly emailJobService: EmailJobService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectRepository(GpuAllocationStats)
    readonly gpuAllocationStatsRepository: Repository<GpuAllocationStats>,
    @InjectRepository(GpuDeploymentFailure)
    readonly gpuDeploymentFailureRepository: Repository<GpuDeploymentFailure>,
  ) {}

  async getAvailableGpuResource(body: IGpuResourceSearch): Promise<IAvailableGpuResponseData> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { page_number, page_size, price_range_min, price_range_max, ...rest } = body;
    if (price_range_min || price_range_max) {
      body.page_number = undefined;
      body.page_size = undefined;
    }
    const data = await this.GpuHttpService.getAvailableGpuResource(body);
    const prices = await this.gpusPricingRepository.find();
    const gpusHowPricing = await this.gpuHwPricingRepository.find();
    const allocatedHotkeys = await this.getAllocatedHotkeys();
    const stats = await this.gpuAllocationStatsRepository.find();
    const gpuAllocationStatsMap = new Map<string, IGpuAllocationStats>();
    for (const stat of stats) {
      const gpuStat: IGpuAllocationStats = {
        hotkey: stat.hotkey,
        total_allocation_count: Number(stat.total_allocation_count),
        longest_rental_hours: Number(stat.longest_rental_hours),
        reliability: Number(stat.reliability),
      };
      gpuAllocationStatsMap.set(stat.hotkey, gpuStat);
    }

    const minersList: IGpuFlat[] = [];
    const unpricedMiners: SearchGpuData[] = [];

    for (const miner of data.data.page_items) {
      const pricing = getGpuPricing(prices, miner.gpu_name);

      if (!pricing) {
        prices.push(await this.createGpuPricing(miner.gpu_name, miner.gpu_capacity / miner.gpu_count));
        await this.notifyService.notifyRole(
          UserRole.ADMIN,
          NotificationType.GPU_UNPRICED,
          `New GPU found without Pricing: ${miner.gpu_name} with capacity: ${Math.ceil(miner.gpu_capacity / miner.gpu_count)} GB.`,
        );
        unpricedMiners.push(miner);
        continue;
      }

      if (!pricing.price) {
        continue;
      }

      if (miner.allocate_status !== MinerAvailableText) {
        continue;
      }

      if (allocatedHotkeys.includes(miner.hotkey)) {
        continue;
      }

      const price = calculateGpuPrice(
        gpusHowPricing.length ? gpusHowPricing[0] : this.defaultGpusHowPricing,
        pricing.price,
        Math.ceil(miner.ram),
        miner.cpu_count,
        miner.gpu_count,
      );

      if ((price_range_min && price < price_range_min) || (price_range_max && price > price_range_max)) {
        continue;
      }

      const gpuStat = gpuAllocationStatsMap.get(miner.hotkey);
      minersList.push({
        hotkey: miner.hotkey,
        cpu_count: miner.cpu_count,
        gpu_name: miner.gpu_name.toUpperCase(),
        gpu_capacity: pricing.vram * miner.gpu_count,
        gpu_count: miner.gpu_count,
        ram: Math.ceil(miner.ram),
        hard_disk: Math.ceil(miner.hard_disk),
        price: price,
        total_allocation_count: gpuStat ? gpuStat.total_allocation_count : 0,
        longest_rental_hours: gpuStat ? gpuStat.longest_rental_hours : 0,
        reliability: gpuStat ? gpuStat.reliability : 1,
      });
    }

    if (unpricedMiners.length > 0) {
      const usersAdmin: IUserEntity[] = await this.userRepository.find({
        where: { role: UserRole.ADMIN },
      });

      await Promise.all(
        usersAdmin.map(async (userAdmin) => {
          try {
            await this.emailJobService.sendUnpricedGpuFoundEmail(
              userAdmin.email,
              `${userAdmin.first_name} ${userAdmin.last_name}`,
              unpricedMiners,
            );
          } catch (error) {
            this.logger.warn(`Failed to send email to ${userAdmin.email}: ${error.message}`);
          }
        }),
      );
    }

    if (price_range_min || price_range_max) {
      if (!page_number || !page_size) {
        return {
          page_items: minersList,
          page_number: 1,
          page_size: minersList.length,
          next_page_number: null,
        };
      }

      const skip = (page_number - 1) * page_size;
      const take = page_size;
      const selectedMiners = minersList.slice(skip, skip + take);
      return {
        page_items: selectedMiners,
        page_number: page_number,
        page_size: selectedMiners.length,
        next_page_number: minersList.length > skip + take ? page_number + 1 : null,
      };
    }

    return {
      page_items: minersList,
      page_number: data.data.page_number,
      page_size: minersList.length,
      next_page_number: data.data.next_page_number,
    };
  }

  async deployGpuResource(userId: string, deployGpuDto: DeployGpuDto) {
    const hotkeyBeingAllocating = await this.cacheManager.get(generateGpuBeingAllocatedCacheKey(deployGpuDto.hotkey));
    if (hotkeyBeingAllocating) {
      throw new BadRequestException('Resource already being allocated. Please try again later.');
    }

    await this.cacheManager.set(generateGpuBeingAllocatedCacheKey(deployGpuDto.hotkey), deployGpuDto.hotkey);

    try {
      // External service calls before connecting to the database
      const existingGpuByHotkey = await this.gpusRentedRepository.findOne({
        where: { hotkey: deployGpuDto.hotkey, is_allocated: true },
      });
      if (existingGpuByHotkey) {
        throw new BadRequestException('Resource already allocated');
      }

      const sshKey = deployGpuDto.ssh_key_id
        ? await this.sshKeyRepository.findOne({ where: { ssh_key_id: deployGpuDto.ssh_key_id } })
        : null;
      if (!sshKey && deployGpuDto.ssh_key_id) {
        throw new NotFoundException('SSH key not found');
      }

      const user = await this.userRepository.findOne({ where: { user_id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (user.balance < 0) {
        throw new BadRequestException('Insufficient balance');
      }

      const resourceData = await this.GpuHttpService.getGpuResourceDetail(deployGpuDto.hotkey);
      if (!resourceData.data.page_items.length) {
        throw new NotFoundException('Resource not found');
      }

      let minerSpecsData = convertMinerDataToArray(resourceData.data.page_items[0])[0];
      if (resourceData.data.page_items.length > 1 && !isValidMinerData(minerSpecsData)) {
        for (let i = 1; i < resourceData.data.page_items.length; i++) {
          minerSpecsData = convertMinerDataToArray(resourceData.data.page_items[i])[0];
          if (isValidMinerData(minerSpecsData)) {
            break;
          }
        }
      }

      if (!isValidMinerData(minerSpecsData)) {
        throw new NotFoundException('Resource not found');
      }

      const gpusHowPricing = await this.gpuHwPricingRepository.find();
      const gpuPricing = await this.gpusPricingRepository.findOne({
        where: { model: minerSpecsData.specs.gpu.details[0]?.name.toLowerCase() ?? '' },
      });

      if (!gpuPricing || !gpuPricing.price) {
        throw new NotFoundException('Pricing information not found');
      }

      const computedRam = roundUpToWhole(convertBytesToGB(minerSpecsData.specs.ram.total));
      const computedGpuCapacity = roundUpToWhole(gpuPricing.vram * minerSpecsData.specs.gpu.count);
      const computedHardDisk = roundUpToWhole(convertBytesToGB(minerSpecsData.specs.hard_disk.total));
      const resourcePrice = calculateGpuPrice(
        gpusHowPricing.length ? gpusHowPricing[0] : this.defaultGpusHowPricing,
        gpuPricing.price,
        computedRam,
        minerSpecsData.specs.cpu.count,
        minerSpecsData.specs.gpu.count,
      );
      const resourcePriceInCents = roundToNearestCent(resourcePrice);
      const balanceMonitorIntervalInHours = this.balanceMonitorConfig.balanceMonitorIntervalInMinutes / 60;
      const currentCost = await this.costService.getCurrentCost(user.user_id);
      const nextIntervalCostInCents = parseFloat((currentCost * 100 * balanceMonitorIntervalInHours).toFixed(1));

      if (user.balance < resourcePriceInCents || user.balance < nextIntervalCostInCents) {
        throw new BadRequestException('Insufficient balance');
      }

      const gpuModel = await this.getOrCreateGpuModel(minerSpecsData, computedRam, gpuPricing);

      const sshKeyValue = sshKey?.ssh_key || null; // Null if ssh_key is not provided (as it is optional)
      if (!sshKeyValue) {
        // deployment without an SSH key, check miner's version compatibility
        const miners: IMinerStatus[] = await this.GpuHttpService.checkMinerStatus([deployGpuDto.hotkey], true);
        const firstMiner = miners[0];
        if (firstMiner && (!firstMiner.version || firstMiner.version < MIN_MINER_VERSION)) {
          throw new BadRequestException('Deployment without an SSH key require the miner to be updated to the latest version');
        }
      }

      const data = await this.GpuHttpService.deployGpuResource(deployGpuDto.hotkey, sshKeyValue);

      const newGpuRented = new GpusRented();
      newGpuRented.user_id = userId;
      newGpuRented.name = deployGpuDto.name;
      newGpuRented.model = constructRentedGpuModelName(
        minerSpecsData.specs.gpu.details[0]?.name,
        computedGpuCapacity,
        minerSpecsData.specs.cpu.count,
        computedRam,
      );
      newGpuRented.gpu_model_id = gpuModel.gpus_model_id;
      newGpuRented.gpu_model = minerSpecsData.specs.gpu.details[0]?.name.toLowerCase();
      newGpuRented.hotkey = minerSpecsData.hotkey;
      newGpuRented.is_allocated = true;
      newGpuRented.status = minerSpecsData.state;
      newGpuRented.public_ip = data.data.ssh_ip;
      newGpuRented.port = data.data.ssh_port;
      newGpuRented.gpu_pricing_id = gpuPricing.gpu_pricing_id;
      newGpuRented.price = resourcePriceInCents;
      newGpuRented.ram = computedRam;
      newGpuRented.cpu_count = minerSpecsData.specs.cpu.count;
      newGpuRented.gpu_capacity = computedGpuCapacity;
      newGpuRented.gpu_count = minerSpecsData.specs.gpu.count;
      newGpuRented.hard_disk = computedHardDisk;
      newGpuRented.allocation_uuid = data.data.uuid_key;
      newGpuRented.status = ONLINE;
      newGpuRented.ssh_key_id = deployGpuDto.ssh_key_id || null;
      newGpuRented.miner_version = data.data.miner_version || null;

      await this.gpusRentedRepository.save(newGpuRented);
      await this.gpuChartsService.addRentedToGpuCharts(gpuModel.gpus_model_id);
      await this.paymentService.createOrUpdateRentalPayment(user.user_id, newGpuRented);
      await this.gpuUsageSessionsService.createNewGpuUsageOnRent(newGpuRented.gpu_rented_id, newGpuRented.created_at);

      try {
        const newCurrentCost = await this.costService.getCurrentCost(user.user_id);
        this.notificationGateway.updateCurrentCost(user.user_id, newCurrentCost);
        this.notifyService.notifyUser(
          newGpuRented.user_id,
          NotificationType.GPU_ALLOCATED,
          `Your GPU (${newGpuRented.name}) has been deployed successfully.`,
        );
        if (!deployGpuDto?.ssh_key_id) {
          // Send a notification if deployed without an SSH public key
          this.notifyService.notifyGpuDeployedWithoutSshKey(
            newGpuRented.user_id,
            `You have deployed a GPU (${newGpuRented.name}) without adding an SSH key. While you can access the machine using our built-in SSH-in-browser CLI, an SSH key is required for access from your local CLI.`,
            `/gpu/rented/#${newGpuRented.gpu_rented_id}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error during GPU resource deployment while updating current cost: ${deployGpuDto.hotkey} ${error.message}`,
          error,
        );
      }

      const userCurrentRunway = await this.gpuUtilityService.calculateCurrentRunwayTime(user.user_id);
      this.notificationGateway.updateCurrentRunway(user.user_id, userCurrentRunway);

      return newGpuRented;
    } catch (error) {
      this.logger.warn(`Error during GPU resource deployment: ${deployGpuDto.hotkey} ${error.message}`, error);
      if (error instanceof HttpException) {
        this.createGpuDeploymentFailure(error, deployGpuDto.hotkey);
      }
      throw error;
    } finally {
      try {
        await this.cacheManager.del(generateGpuBeingAllocatedCacheKey(deployGpuDto.hotkey));
      } catch (error) {
        this.logger.error(`Error deleting cache while deploy gpu: ${deployGpuDto.hotkey} ${error.message}`, error);
      }
    }
  }

  async deallocateGpuResource(userId: string, gpu_rented_id: string, deallocateBy: GpuDeallocateByEnum = GpuDeallocateByEnum.USER) {
    try {
      const user = await this.userRepository.findOne({ where: { user_id: userId } });

      if (!user) {
        throw new NotFoundException('User not found');
      }
      const gpuRented = await this.gpusRentedRepository.findOne({ where: { gpu_rented_id, user_id: userId } });

      if (!gpuRented) {
        throw new NotFoundException('Gpu not found');
      }
      if (!gpuRented.is_allocated) {
        throw new BadRequestException('Gpu is already deallocated');
      }

      if (!gpuRented.allocation_uuid) {
        throw new BadRequestException('Allocation information not found');
      }

      const data = await this.GpuHttpService.deallocateGpuResource(gpuRented.hotkey, gpuRented.allocation_uuid);
      const now = new Date();
      if (!data.success) {
        const register_api_allocationsData = await this.GpuHttpService.getAllocatedHotkeys();
        if (!register_api_allocationsData.data?.length) {
          register_api_allocationsData.data = [];
        }
        const register_api_allocationsHotkeys = register_api_allocationsData.data;
        const hotkey = gpuRented.hotkey;
        const allocation = register_api_allocationsHotkeys.includes(hotkey);
        if (!allocation) {
          Object.assign(gpuRented, {
            is_allocated: false,
            deallocated_by: deallocateBy,
            deallocatable_on_api: false,
            deallocated_at: now,
            ssh_key_id: null,
          });

          this.updateRentedGpu(gpuRented.gpu_rented_id, {
            is_allocated: gpuRented.is_allocated,
            deallocated_by: gpuRented.deallocated_by,
            deallocatable_on_api: gpuRented.deallocatable_on_api,
            deallocated_at: gpuRented.deallocated_at,
            ssh_key_id: gpuRented.ssh_key_id,
          });

          const currentCost = await this.costService.getCurrentCost(user.user_id);
          this.notificationGateway.updateCurrentCost(user.user_id, currentCost);

          if (deallocateBy === GpuDeallocateByEnum.SSH_KEY_DELETION) {
            this.notificationGateway.removeDeallocatedGpu(user.user_id, gpuRented.gpu_rented_id);
          }

          this.notifyService.notifyUser(
            gpuRented.user_id,
            NotificationType.GPU_DEALLOCATED,
            deallocateBy === GpuDeallocateByEnum.SSH_KEY_DELETION
              ? `Your GPU (${gpuRented.name}) has been deallocated due to SSH key deletion.`
              : `Your GPU (${gpuRented.name}) has been deallocated successfully.`,
          );

          await this.paymentService.createOrUpdateRentalPayment(user.user_id, gpuRented);
          await this.gpuUsageSessionsService.endGpuUsageOnDeallocation(gpuRented.gpu_rented_id, gpuRented.deallocated_at, 0);

          const userCurrentRunway = await this.gpuUtilityService.calculateCurrentRunwayTime(user.user_id);
          this.notificationGateway.updateCurrentRunway(user.user_id, userCurrentRunway);

          return {
            success: true,
            message: 'Resource deallocated successfully',
          };
        }

        throw new InternalServerErrorException(data.message);
      }

      gpuRented.is_allocated = false;
      gpuRented.deallocated_by = deallocateBy;
      gpuRented.deallocated_at = now;
      gpuRented.ssh_key_id = null;

      let addedCost = 0;
      const { shouldDeductCost, secondsDifference, referenceDate } = this.getShouldDeductCostAndMinutesDifference(gpuRented, new Date());
      if (shouldDeductCost && secondsDifference > 0) {
        const cost = costBasedOnSeconds(secondsDifference, gpuRented.price);
        addedCost = cost;
        const totalCostInCents: number = convertCostToCents(cost);

        gpuRented.last_payment_date = addSeconds(referenceDate, secondsDifference);
        gpuRented.total_cost = gpuRented.total_cost ? gpuRented.total_cost + cost : cost;
        gpuRented.total_hours = gpuRented.total_hours
          ? gpuRented.total_hours + secondsToHours(secondsDifference)
          : secondsToHours(secondsDifference);

        await this.userRepository
          .createQueryBuilder()
          .update(User)
          .set({ balance: () => `balance - ${totalCostInCents}` })
          .where('user_id = :id', { id: user.user_id })
          .execute();

        user.balance = user.balance - totalCostInCents;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        await this.costService.createOrUpdateCost(user, secondsToHours(secondsDifference), cost, today);
        await this.earningService.createOrUpdateEarning(new Date(), cost);
        await this.gpuChartsService.addEarnedAndHoursToGpuCharts(gpuRented.gpu_model_id, cost, secondsToHours(secondsDifference));
      }
      this.updateRentedGpu(gpuRented.gpu_rented_id, {
        is_allocated: gpuRented.is_allocated,
        deallocated_by: gpuRented.deallocated_by,
        last_payment_date: gpuRented.last_payment_date,
        total_cost: gpuRented.total_cost,
        total_hours: gpuRented.total_hours,
        deallocated_at: gpuRented.deallocated_at,
        ssh_key_id: gpuRented.ssh_key_id,
      });

      await this.paymentService.createOrUpdateRentalPayment(user.user_id, gpuRented);
      await this.gpuUsageSessionsService.endGpuUsageOnDeallocation(gpuRented.gpu_rented_id, gpuRented.deallocated_at, addedCost);

      if (deallocateBy === GpuDeallocateByEnum.SSH_KEY_DELETION) {
        this.notificationGateway.removeDeallocatedGpu(user.user_id, gpuRented.gpu_rented_id);
      }

      this.notifyService.notifyUser(
        gpuRented.user_id,
        NotificationType.GPU_DEALLOCATED,
        deallocateBy === GpuDeallocateByEnum.SSH_KEY_DELETION
          ? `Your GPU (${gpuRented.name}) has been deallocated due to SSH key deletion.`
          : `Your GPU (${gpuRented.name}) has been deallocated successfully.`,
      );

      const currentCost = await this.costService.getCurrentCost(user.user_id);
      this.notificationGateway.updateCurrentCost(user.user_id, currentCost);

      return data;
    } catch (error) {
      this.logger.error(`Error deallocating GPU resource: ${error}`);
      throw error;
    }
  }

  async listDeployedGpuResource(userId: string, search: IRentedGpuSearch): Promise<IRentedListPagination> {
    try {
      const {
        name,
        cpu_count_min,
        cpu_count_max,
        gpu_capacity_min,
        gpu_capacity_max,
        hard_disk_total_min,
        hard_disk_total_max,
        ram_total_min,
        ram_total_max,
        price_range_min,
        price_range_max,
        skip,
        take,
        sortBy,
        sortDirection,
      } = search;

      let gpuRented = this.gpusRentedRepository
        .createQueryBuilder('gpus_rented')
        .where('gpus_rented.user_id = :userId', { userId })
        .andWhere('gpus_rented.is_allocated = :is_allocated', { is_allocated: true });

      if (name) {
        gpuRented = gpuRented.andWhere('gpus_rented.name ILIKE :name', { name: `%${name}%` });
      }
      if (cpu_count_min) {
        gpuRented = gpuRented.andWhere('gpus_rented.cpu_count >= :cpu_count_min', { cpu_count_min });
      }
      if (cpu_count_max) {
        gpuRented = gpuRented.andWhere('gpus_rented.cpu_count <= :cpu_count_max', { cpu_count_max });
      }
      if (gpu_capacity_min) {
        gpuRented = gpuRented.andWhere('gpus_rented.gpu_capacity >= :gpu_capacity_min', { gpu_capacity_min });
      }
      if (gpu_capacity_max) {
        gpuRented = gpuRented.andWhere('gpus_rented.gpu_capacity <= :gpu_capacity_max', { gpu_capacity_max });
      }
      if (hard_disk_total_min) {
        gpuRented = gpuRented.andWhere('gpus_rented.hard_disk >= :hard_disk_total_min', { hard_disk_total_min });
      }
      if (hard_disk_total_max) {
        gpuRented = gpuRented.andWhere('gpus_rented.hard_disk <= :hard_disk_total_max', { hard_disk_total_max });
      }
      if (ram_total_min) {
        gpuRented = gpuRented.andWhere('gpus_rented.ram >= :ram_total_min', { ram_total_min });
      }
      if (ram_total_max) {
        gpuRented = gpuRented.andWhere('gpus_rented.ram <= :ram_total_max', { ram_total_max });
      }
      if (price_range_min) {
        gpuRented = gpuRented.andWhere('gpus_rented.price >= :price_range_min', { price_range_min });
      }
      if (price_range_max) {
        gpuRented = gpuRented.andWhere('gpus_rented.price <= :price_range_max', { price_range_max });
      }

      const sortColumn = sortBy ? sortBy : 'created_at';
      const sortOrder = sortDirection === 'desc' ? 'DESC' : 'ASC';

      gpuRented = gpuRented.orderBy(`gpus_rented.${sortColumn}`, sortOrder);

      const count = await gpuRented.getCount();
      let hasMore = true;
      if (skip !== undefined && take !== undefined && count <= skip + take) {
        hasMore = false;
      }
      const rentedGpus = await gpuRented.take(take).skip(skip).getMany();

      // Check if the user has at least one GPU rented
      const countAllUserRentedGpus = await this.gpusRentedRepository
        .createQueryBuilder('gpus_rented')
        .where('gpus_rented.user_id = :userId', { userId })
        .andWhere('gpus_rented.is_allocated = :isAllocated', { isAllocated: true })
        .getCount();

      return {
        at_least_one_gpu_rented: countAllUserRentedGpus > 0,
        rented_gpus: rentedGpus,
        has_more: hasMore,
      };
    } catch (error) {
      console.error('Error listing deployed GPU resources:', error);
      // Handle specific error types if needed, otherwise throw a general internal server error
      throw new InternalServerErrorException('An error occurred while listing deployed GPU resources');
    }
  }

  async getGpuResourceByHotkey(hotkey: string): Promise<IGpuFlat> {
    const resourceData = await this.GpuHttpService.getGpuResourceDetail(hotkey);
    if (!resourceData.data.page_items.length) {
      throw new NotFoundException('Resource not found');
    }
    let minerSpecsData = convertMinerDataToArray(resourceData.data.page_items[0])[0];

    if (resourceData.data.page_items.length > 1 && !isValidMinerData(minerSpecsData)) {
      for (let i = 1; i < resourceData.data.page_items.length; i++) {
        minerSpecsData = convertMinerDataToArray(resourceData.data.page_items[i])[0];
        if (isValidMinerData(minerSpecsData)) {
          break;
        }
      }
    }

    if (!isValidMinerData(minerSpecsData)) {
      throw new NotFoundException('Resource not found');
    }

    const pricing = await this.gpusPricingRepository.findOne({
      where: {
        model: minerSpecsData.specs.gpu.details[0]?.name.toLowerCase() ?? '',
      },
    });
    const gpusHowPricing = await this.gpuHwPricingRepository.find();

    if (!pricing?.price) {
      throw new NotFoundException('Pricing information not found');
    }

    const gpuStat = await this.gpuAllocationStatsRepository.findOne({
      where: { hotkey },
    });

    return {
      hotkey: minerSpecsData.hotkey,
      cpu_count: minerSpecsData.specs.cpu.count,
      gpu_name: minerSpecsData.specs.gpu.details[0]?.name ?? '',
      gpu_capacity: pricing.vram * minerSpecsData.specs.gpu.count,
      gpu_count: minerSpecsData.specs.gpu.count,
      ram: Math.ceil(convertBytesToGB(minerSpecsData.specs.ram.total)),
      hard_disk: Math.ceil(convertBytesToGB(minerSpecsData.specs.hard_disk.total)),
      price: calculateGpuPrice(
        gpusHowPricing.length ? gpusHowPricing[0] : this.defaultGpusHowPricing,
        pricing.price,
        Math.ceil(convertBytesToGB(minerSpecsData.specs.ram.total)),
        minerSpecsData.specs.cpu.count,
        minerSpecsData.specs.gpu.count,
      ),
      total_allocation_count: gpuStat ? gpuStat.total_allocation_count : 0,
      longest_rental_hours: gpuStat ? gpuStat.longest_rental_hours : 0,
      reliability: gpuStat ? gpuStat.reliability : 1,
    };
  }

  async editRentedGpu(userId: string, gpu_rented_id: string, updateRentedGpuDto: UpdateRentedGpuDto) {
    const { name, ssh_key_id } = updateRentedGpuDto;
    const gpuRented = await this.gpusRentedRepository.findOne({
      where: {
        gpu_rented_id,
        user_id: userId,
      },
    });

    if (!gpuRented) {
      throw new NotFoundException('Gpu not found');
    }
    if (!gpuRented.is_allocated) {
      throw new BadRequestException('Gpu is already deallocated');
    }
    if (!gpuRented.allocation_uuid) {
      throw new BadRequestException('Allocation information not found');
    }

    if (name) {
      gpuRented.name = name;
    }
    if (ssh_key_id) {
      const sshKey = await this.sshKeyRepository.findOne({
        where: { ssh_key_id },
      });

      if (!sshKey) {
        throw new NotFoundException('SSH key not found');
      }
      const data = await this.GpuHttpService.changeSshKey(gpuRented.hotkey, gpuRented.allocation_uuid, sshKey.ssh_key);
      if (!data.success) {
        throw new InternalServerErrorException(data.message);
      }
      gpuRented.ssh_key_id = sshKey.ssh_key_id;
    }

    await this.updateRentedGpu(gpuRented.gpu_rented_id, {
      name: gpuRented.name,
      ssh_key_id: gpuRented.ssh_key_id,
    });

    return gpuRented;
  }

  private async createGpuPricing(model: string, vram: number): Promise<GpusPricing> {
    const newGpuPricing = new GpusPricing();
    newGpuPricing.model = model.toLowerCase();
    newGpuPricing.vram = Math.ceil(vram);

    return this.gpusPricingRepository.save(newGpuPricing);
  }

  async getUserHistoricalCosts(userId: string, period: IPeriod): Promise<ICostState> {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    const days = periodToDays(period) - 1;
    const startDate = getTheStartOfDay(subDays(now, days));
    const endDate = getTheEndOfDay(now);

    const historicalCosts = await this.gpuCostHistoryRepository.find({
      where: {
        user_id: userId,
        date_cost_history: And(MoreThanOrEqual(startDate), LessThanOrEqual(endDate)),
      },
      order: { date_cost_history: 'ASC' },
    });

    if (historicalCosts.length === 0) {
      const defaultCosts = Array.from({ length: days + 1 }, (_, i) => {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        return { date, cost: 0 };
      });

      const data: [number, number][] = defaultCosts.map((entry) => {
        return [entry.date.getTime(), entry.cost];
      });
      return generateSerie(period, data);
    }

    const costData = Array.from({ length: days + 1 }, (_, i) => {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      const costEntry = historicalCosts.filter((entry) => {
        const entryDate = new Date(entry.date_cost_history);
        entryDate.setHours(0, 0, 0, 0);
        return entryDate.getTime() === date.getTime();
      });

      return { date, cost: costEntry.reduce((acc, entry) => acc + entry.cost, 0) };
    });

    const data: [number, number][] = costData.map((entry) => {
      return [entry.date.getTime(), entry.cost];
    });

    return generateSerie(period, data);
  }

  async getAverageCost(userId: string): Promise<IAverageCost> {
    const gpuCostHistory = await this.gpuCostHistoryRepository.find({
      where: {
        user_id: userId,
      },
    });

    const currentRunway = await this.gpuUtilityService.calculateCurrentRunwayTime(userId);
    return {
      currentRunway,
    };
  }

  async getResourceUsageForUser(userId: string): Promise<IResourceUsage> {
    try {
      const result = await this.gpusRentedRepository
        .createQueryBuilder('gpus_rented')
        .select('SUM(gpus_rented.cpu_count)', 'cpu_count')
        .addSelect('SUM(gpus_rented.gpu_count)', 'gpu_count')
        .addSelect('SUM(gpus_rented.hard_disk)', 'hard_disk')
        .where('gpus_rented.user_id = :userId', { userId })
        .andWhere('gpus_rented.is_allocated = :isAllocated', { isAllocated: true })
        .getRawOne();

      return {
        cpu_count: parseInt(result.cpu_count, 10) || 0,
        gpu_count: parseInt(result.gpu_count, 10) || 0,
        hard_disk: parseInt(result.hard_disk, 10) || 0,
      };
    } catch (error) {
      // Handle the error appropriately
      console.error('Error fetching resource usage:', error);
      throw new Error('Could not fetch resource usage');
    }
  }

  async restartGpuResource(userId: string, gpu_rented_id: string): Promise<IGpusRented> {
    const gpuRented = await this.gpusRentedRepository.findOne({
      where: {
        gpu_rented_id,
        user_id: userId,
      },
    });

    if (!gpuRented) {
      throw new NotFoundException('Gpu not found');
    }
    if (!gpuRented.is_allocated) {
      throw new BadRequestException('Gpu is already deallocated');
    }

    if (!gpuRented.allocation_uuid) {
      throw new BadRequestException('Allocation information not found');
    }

    const data = await this.GpuHttpService.restartGpuResource(gpuRented.hotkey, gpuRented.allocation_uuid);

    if (!data.success) {
      throw new InternalServerErrorException(data.message);
    }

    return gpuRented;
  }

  getShouldDeductCostAndMinutesDifference(
    gpuRented: GpusRented,
    now: Date,
  ): {
    shouldDeductCost: boolean;
    secondsDifference: number;
    referenceDate: Date;
    updateCost: boolean;
  } {
    const GRACE_PERIOD_MINUTES = parseInt(process.env.GRACE_PERIOD_MINUTES || '30', 10);
    let referenceDate = gpuRented.last_payment_date ? new Date(gpuRented.last_payment_date) : new Date(gpuRented.created_at);
    const lastDeductionTime = referenceDate;
    let secondsDifference = differenceInSeconds(now, lastDeductionTime);
    const shouldDeductCost = true;
    let updateCost = false;

    if (gpuRented.is_payment_suspended) {
      return {
        shouldDeductCost: false,
        secondsDifference: secondsDifference,
        referenceDate: referenceDate,
        updateCost: updateCost,
      };
    }

    if (gpuRented.status === ONLINE && gpuRented.online_status_received_at) {
      const gracePaymentPeriodEndAt = addMinutes(gpuRented.online_status_received_at, GRACE_PERIOD_MINUTES);

      if (isBefore(now, gracePaymentPeriodEndAt)) {
        return {
          shouldDeductCost: false,
          secondsDifference: secondsDifference,
          referenceDate: referenceDate,
          updateCost: updateCost,
        };
      }

      if (gpuRented.last_payment_date) {
        if (isAfter(gpuRented.last_payment_date, gracePaymentPeriodEndAt)) {
          secondsDifference = differenceInSeconds(now, gpuRented.last_payment_date);
          referenceDate = new Date(gpuRented.last_payment_date);
        } else {
          secondsDifference = differenceInSeconds(now, gracePaymentPeriodEndAt);
          referenceDate = new Date(gracePaymentPeriodEndAt);
          updateCost = true;
        }
      } else {
        secondsDifference = differenceInSeconds(now, gracePaymentPeriodEndAt);
        referenceDate = new Date(gracePaymentPeriodEndAt);
        updateCost = true;
      }
    }

    return {
      shouldDeductCost,
      secondsDifference,
      referenceDate,
      updateCost,
    };
  }

  async deallocateByWebhook(gpuRented: GpusRented, user: User, deallocated_at: Date): Promise<GpusRented> {
    gpuRented.is_allocated = false;
    gpuRented.deallocated_by = GpuDeallocateByEnum.WEBHOOK;
    gpuRented.ssh_key_id = null;

    let addedCost = 0;
    const { shouldDeductCost, secondsDifference, referenceDate } = this.getShouldDeductCostAndMinutesDifference(gpuRented, new Date());
    if (shouldDeductCost && secondsDifference > 0) {
      const cost = costBasedOnSeconds(secondsDifference, gpuRented.price);
      addedCost = cost;
      const totalCostInCents: number = convertCostToCents(cost);

      gpuRented.last_payment_date = addSeconds(referenceDate, secondsDifference);
      gpuRented.total_cost = gpuRented.total_cost ? gpuRented.total_cost + cost : cost;
      gpuRented.total_hours = gpuRented.total_hours
        ? gpuRented.total_hours + secondsToHours(secondsDifference)
        : secondsToHours(secondsDifference);
      await this.userRepository
        .createQueryBuilder()
        .update(User)
        .set({ balance: () => `balance - ${totalCostInCents}` })
        .where('user_id = :id', { id: user.user_id })
        .execute();
      user.balance = user.balance - totalCostInCents;
      gpuRented.user = user;

      this.logger.log(
        `Webhook : Deducted cost of '${totalCostInCents}' in cents for gpu '${
          gpuRented.gpu_rented_id
        }' when deallocating with webhook. Timestamp: ${new Date().toISOString()}`,
        {
          gpu_rented_id: gpuRented.gpu_rented_id,
          deallocated_at,
        },
      );

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      await this.costService.createOrUpdateCost(user, secondsToHours(secondsDifference), cost, today);
      await this.earningService.createOrUpdateEarning(new Date(), cost);
      await this.gpuChartsService.addEarnedAndHoursToGpuCharts(gpuRented.gpu_model_id, cost, secondsToHours(secondsDifference));
    }
    gpuRented.deallocated_at = deallocated_at;

    await this.updateRentedGpu(gpuRented.gpu_rented_id, {
      is_allocated: gpuRented.is_allocated,
      deallocated_by: gpuRented.deallocated_by,
      last_payment_date: gpuRented.last_payment_date,
      total_cost: gpuRented.total_cost,
      total_hours: gpuRented.total_hours,
      deallocated_at: gpuRented.deallocated_at,
      ssh_key_id: gpuRented.ssh_key_id,
    });

    await this.paymentService.createOrUpdateRentalPayment(user.user_id, gpuRented);
    await this.gpuUsageSessionsService.endGpuUsageOnDeallocation(gpuRented.gpu_rented_id, gpuRented.deallocated_at, addedCost);

    const userCurrentRunway = await this.gpuUtilityService.calculateCurrentRunwayTime(user.user_id);
    this.notificationGateway.updateCurrentRunway(user.user_id, userCurrentRunway);

    return gpuRented;
  }

  async getAllocatedHotkeys(): Promise<string[]> {
    return await this.gpusRentedRepository
      .createQueryBuilder('gpusRented')
      .select('hotkey')
      .where('gpusRented.is_allocated = :isAllocated', { isAllocated: true })
      .getRawMany()
      .then((results) => results.map((result) => result.hotkey));
  }

  async checkMinerStatus(hotkey: string): Promise<IGpuStatus> {
    try {
      const minerStatus = await this.GpuHttpService.checkMinerStatus([hotkey]);
      const status = minerStatus.find((status) => status.hotkey === hotkey)?.status;
      const gpuStatus = status?.includes(ONLINE) ? ONLINE : OFFLINE;
      return {
        hotkey,
        status: gpuStatus,
      };
    } catch (error) {
      this.logger.error(`Error checking miner status: ${error}`);
      return {
        hotkey: hotkey,
        status: OFFLINE,
      };
    }
  }

  private async getOrCreateGpuModel(minerSpecsData: IMinerSpecsData, computedRam: number, gpuPricing: GpusPricing): Promise<GpusModels> {
    try {
      let gpuModel = await this.gpusModelsRepository.findOne({
        where: {
          gpu_model: minerSpecsData.specs.gpu.details[0]?.name.toUpperCase(),
          ram: computedRam,
          cpu: minerSpecsData.specs.cpu.count,
        },
      });

      if (!gpuModel) {
        gpuModel = new GpusModels();
        gpuModel.gpu_model = minerSpecsData.specs.gpu.details[0]?.name.toUpperCase();
        gpuModel.ram = computedRam;
        gpuModel.cpu = minerSpecsData.specs.cpu.count;
        gpuModel.gpu_pricing_id = gpuPricing.gpu_pricing_id;

        return this.gpusModelsRepository.save(gpuModel);
      }

      return gpuModel;
    } catch (error) {
      this.logger.error(`Error getting or creating GPU model: ${error}`, error);
      throw error;
    }
  }

  async checkMinerStatusAndUpdateDb(gpu_rented_id: string): Promise<IGpuStatus> {
    try {
      const gpuRented = await this.gpusRentedRepository.findOne({
        where: {
          gpu_rented_id: gpu_rented_id,
        },
      });

      if (!gpuRented) {
        throw new NotFoundException('Gpu not found');
      }

      const minerStatus = await this.GpuHttpService.checkMinerStatus([gpuRented.hotkey]);
      const status = minerStatus.find((status) => status.hotkey === gpuRented.hotkey)?.status;
      const gpuStatus = status?.includes(ONLINE) ? ONLINE : OFFLINE;

      if (gpuStatus === ONLINE && gpuRented.status === OFFLINE) {
        gpuRented.is_payment_suspended = false;
        gpuRented.online_status_received_at = new Date();
        gpuRented.offline_status_received_at = null;
        gpuRented.status = ONLINE;

        await this.updateRentedGpu(gpuRented.gpu_rented_id, {
          is_payment_suspended: gpuRented.is_payment_suspended,
          offline_status_received_at: gpuRented.offline_status_received_at,
          online_status_received_at: gpuRented.online_status_received_at,
          status: gpuRented.status,
        });

        this.notifyService.notifyUser(
          gpuRented.user_id,
          NotificationType.GPU_GET_ONLINE,
          `Your GPU (${gpuRented.name}) is now online. We will resume the payment for this GPU.`,
        );
        this.notifyCurrentCost(gpuRented.user_id);

        this.logger.log(
          `checkMinerStatusAndUpdateDb : Received online status for gpu'${gpuRented.gpu_rented_id}' and hotkey '${
            gpuRented.hotkey
          }' on ${new Date().toISOString()}`,
          {
            gpu_rented_id: gpuRented.gpu_rented_id,
            hotkey: gpuRented.hotkey,
          },
        );
      } else if (gpuStatus === OFFLINE && gpuRented.status === ONLINE) {
        gpuRented.is_payment_suspended = true;
        gpuRented.offline_status_received_at = new Date();
        gpuRented.online_status_received_at = null;
        gpuRented.status = OFFLINE;

        await this.updateRentedGpu(gpuRented.gpu_rented_id, {
          is_payment_suspended: gpuRented.is_payment_suspended,
          offline_status_received_at: gpuRented.offline_status_received_at,
          online_status_received_at: gpuRented.online_status_received_at,
          status: gpuRented.status,
        });

        await this.notifyCurrentCost(gpuRented.user_id);

        this.notifyService.notifyUser(
          gpuRented.user_id,
          NotificationType.GPU_GET_OFFLINE,
          `Your GPU (${gpuRented.name}) went offline. We have suspended the payment for this GPU.`,
        );

        this.logger.log(
          `checkMinerStatusAndUpdateDb : Get offline status for gpu'${gpuRented.gpu_rented_id}' and hotkey '${
            gpuRented.hotkey
          }' on ${new Date().toISOString()}`,
          {
            gpu_rented_id: gpuRented.gpu_rented_id,
            hotkey: gpuRented.hotkey,
          },
        );
      }
      return {
        hotkey: gpuRented.hotkey,
        status: gpuStatus,
      };
    } catch (error) {
      this.logger.error(`Error checking miner status in GpuService: ${error}`);
      throw error;
    }
  }

  private async notifyCurrentCost(user_id: string) {
    try {
      const currentCost = await this.costService.getCurrentCost(user_id);
      console.log('currentCost', currentCost);
      this.notificationGateway.updateCurrentCost(user_id, currentCost);
    } catch (error) {
      this.logger.error(`Error while notifying current cost for user ${user_id}`, error);
    }
  }

  async updateRentedGpu(gpu_rented_id: string, rentedGpu: Partial<GpusRented>) {
    return this.gpusRentedRepository.update({ gpu_rented_id }, rentedGpu);
  }

  async deallocateGpuDueToLowBalance(user_id: string, gpuRented: GpusRented) {
    try {
      if (!gpuRented.allocation_uuid) {
        throw new InternalServerErrorException('Allocation UUID is undefined');
      }
      const data = await this.GpuHttpService.deallocateGpuResource(gpuRented.hotkey, gpuRented.allocation_uuid);
      const now = new Date();
      if (!data.success) {
        const register_api_allocationsData = await this.GpuHttpService.getAllocatedHotkeys();
        const register_api_allocationsHotkeys = register_api_allocationsData.data;
        const hotkey = gpuRented.hotkey;
        const allocation = register_api_allocationsHotkeys.includes(hotkey);
        if (!allocation) {
          Object.assign(gpuRented, {
            is_allocated: false,
            deallocated_by: GpuDeallocateByEnum.BALANCE_MONITOR,
            deallocatable_on_api: false,
            deallocated_at: now,
            ssh_key_id: null,
          });
          this.updateRentedGpu(gpuRented.gpu_rented_id, {
            is_allocated: gpuRented.is_allocated,
            deallocated_by: gpuRented.deallocated_by,
            deallocatable_on_api: gpuRented.deallocatable_on_api,
            deallocated_at: gpuRented.deallocated_at,
            ssh_key_id: gpuRented.ssh_key_id,
          });
          await this.paymentService.createOrUpdateRentalPayment(user_id, gpuRented);
          await this.gpuUsageSessionsService.endGpuUsageOnDeallocation(gpuRented.gpu_rented_id, gpuRented.deallocated_at, 0);

          this.notificationGateway.removeDeallocatedGpu(user_id, gpuRented.gpu_rented_id);
          try {
            const currentCost = await this.costService.getCurrentCost(user_id);
            this.notificationGateway.updateCurrentCost(user_id, currentCost);
          } catch (error) {
            this.logger.error(`Error while notifying current cost for user`, error, {
              gpu_rented_id: gpuRented.gpu_rented_id,
            });
          }
          this.notifyService.notifyUser(
            user_id,
            NotificationType.GPU_DEALLOCATED,
            `Your GPU (${gpuRented.name}) has been deallocated due to low balance.`,
          );

          this.logger.log(`Deallocated GPU resource due to low balance: ${gpuRented.gpu_rented_id}`, {
            gpu_rented_id: gpuRented.gpu_rented_id,
          });

          return;
        }

        throw new InternalServerErrorException(data.message);
      }

      Object.assign(gpuRented, {
        is_allocated: false,
        deallocated_by: GpuDeallocateByEnum.BALANCE_MONITOR,
        deallocated_at: now,
        ssh_key_id: null,
      });

      this.updateRentedGpu(gpuRented.gpu_rented_id, {
        is_allocated: gpuRented.is_allocated,
        deallocated_by: gpuRented.deallocated_by,
        deallocated_at: gpuRented.deallocated_at,
        ssh_key_id: gpuRented.ssh_key_id,
      });

      await this.paymentService.createOrUpdateRentalPayment(user_id, gpuRented);
      await this.gpuUsageSessionsService.endGpuUsageOnDeallocation(gpuRented.gpu_rented_id, gpuRented.deallocated_at, 0);

      this.notificationGateway.removeDeallocatedGpu(user_id, gpuRented.gpu_rented_id);
      try {
        const currentCost = await this.costService.getCurrentCost(user_id);
        this.notificationGateway.updateCurrentCost(user_id, currentCost);
      } catch (error) {
        this.logger.error(`Error while notifying current cost for user`, error, {
          gpu_rented_id: gpuRented.gpu_rented_id,
        });
      }
      this.notifyService.notifyUser(
        user_id,
        NotificationType.GPU_DEALLOCATED,
        `Your GPU (${gpuRented.name}) has been deallocated due to low balance.`,
      );

      const userCurrentRunway = await this.gpuUtilityService.calculateCurrentRunwayTime(user_id);
      this.notificationGateway.updateCurrentRunway(user_id, userCurrentRunway);

      this.logger.log(`Deallocated GPU resource due to low balance: ${gpuRented.gpu_rented_id}`, {
        gpu_rented_id: gpuRented.gpu_rented_id,
      });
    } catch (error) {
      this.logger.error(`Error deallocating GPU resource due to low balance: ${error.message}`, error.stack, {
        user_id: user_id,
        gpu_rented_id: gpuRented.gpu_rented_id,
      });
    }
  }

  async getRentedGpuById(userId: string, gpuRentedId: string): Promise<IGpusRented> {
    try {
      const gpuRented = await this.gpusRentedRepository.findOne({ where: { gpu_rented_id: gpuRentedId, user_id: userId } });
      if (!gpuRented) {
        throw new NotFoundException('Gpu not found');
      }
      return gpuRented;
    } catch (error) {
      throw new InternalServerErrorException('An error occurred while fetching rented GPU by ID');
    }
  }

  async getRentedGpus(): Promise<Array<{ gpu_rented_id: string; miner_version: number | null; hotkey: string }>> {
    const rentedGpus = await this.gpusRentedRepository.find({
      select: ['gpu_rented_id', 'miner_version', 'hotkey'],
      where: { is_allocated: true },
    });
    return rentedGpus;
  }

  async ListPricesGpus(query?: IGpuPricesSearcher): Promise<{ gpu_prices: GpusPricing[]; has_more: boolean }> {
    const { gpu_name, sortBy, sortDirection, skip, take, price_range_max, price_range_min, gpu_capacity_max, gpu_capacity_min } =
      query || {};
    const gpuPricing = this.gpusPricingRepository.createQueryBuilder('gpus_pricing');
    if (gpu_name) {
      gpuPricing.andWhere('gpus_pricing.model LIKE :gpu_name', { gpu_name: `%${gpu_name}%` });
    }
    if (price_range_min) {
      gpuPricing.andWhere('gpus_pricing.price >= :price_range_min', { price_range_min });
    }
    if (price_range_max) {
      gpuPricing.andWhere('gpus_pricing.price <= :price_range_max', { price_range_max });
    }
    if (gpu_capacity_min) {
      gpuPricing.andWhere('gpus_pricing.vram >= :gpu_capacity_min', { gpu_capacity_min });
    }
    if (gpu_capacity_max) {
      gpuPricing.andWhere('gpus_pricing.vram <= :gpu_capacity_max', { gpu_capacity_max });
    }

    let sortColumn = 'gpus_pricing.price';
    let sortOrder: 'ASC' | 'DESC' | undefined = 'ASC';

    if (sortBy) {
      sortColumn = `gpus_pricing.${sortBy}`;
      sortOrder = sortDirection === 'desc' ? 'DESC' : 'ASC';
    } else {
      sortColumn = 'COALESCE(gpus_pricing.price, 0)';
      sortOrder = 'ASC';
    }

    const gpuOrdered = gpuPricing.orderBy(sortColumn, sortOrder);
    const count = await gpuOrdered.getCount();
    let hasMore = true;
    if (skip !== undefined && take !== undefined && count <= skip + take) {
      hasMore = false;
    }
    const gpusPricesList = await gpuOrdered.take(take).skip(skip).getMany();
    return {
      gpu_prices: gpusPricesList,
      has_more: hasMore,
    };
  }

  async updatePricesGpus(gpuPricingId: string, updateGpuPricesDto: UpdateGpuPricesDto): Promise<GpusPricing> {
    const { price, vram } = updateGpuPricesDto;

    const gpuPricing = await this.gpusPricingRepository.findOne({
      where: {
        gpu_pricing_id: gpuPricingId,
      },
    });
    if (!gpuPricing) {
      throw new NotFoundException('Gpu not found');
    }
    if (price) {
      gpuPricing.price = price;
    }
    if (vram) {
      gpuPricing.vram = vram;
    }
    return this.gpusPricingRepository.save(gpuPricing);
  }

  private async createGpuDeploymentFailure(error: HttpException, hotkey: string) {
    try {
      const newDeploymentFailure = new GpuDeploymentFailure();
      newDeploymentFailure.hotkey = hotkey;
      newDeploymentFailure.status_code = error.getStatus();
      newDeploymentFailure.deployment_time = new Date();
      await this.gpuDeploymentFailureRepository.save(newDeploymentFailure);
    } catch (error) {
      this.logger.error(`Error tracking deployment failure: ${hotkey} ${error.message}`, error);
    }
  }

  async getMinerVersion(hotkey: string): Promise<number> {
    try {
      const minerStatus: IMinerStatus[] = await this.GpuHttpService.checkMinerStatus([hotkey], true);
      if (minerStatus[0]?.version) {
        return minerStatus[0].version;
      }
      throw new NotFoundException("Miner's version not found");
    } catch (error) {
      this.logger.error(`Error fetching miner's version: ${hotkey} ${error.message}`, error);
      throw new InternalServerErrorException("Error fetching miner's version");
    }
  }
}
