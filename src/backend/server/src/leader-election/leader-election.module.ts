import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { InstanceIdModule } from '../instance-id';
import { RedisModule } from '../redis';
import { LeaderElectionService } from './leader-election.service';

@Module({
  imports: [InstanceIdModule, RedisModule], // Import InstanceIdModule here
  providers: [LeaderElectionService, Logger],
  exports: [LeaderElectionService], // Export the LeaderElectionService to be used in other modules
})
export class LeaderElectionModule {}
