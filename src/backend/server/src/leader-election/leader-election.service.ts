import { Injectable, Logger, OnModule<PERSON><PERSON>roy } from '@nestjs/common';
import * as Redis from 'ioredis';
import { EnvironmentUtils } from '../core/utils/environment.utils';
import { RedisClientService } from '../redis';

@Injectable()
export class LeaderElectionService implements OnModuleDestroy {
  private readonly logger = new Logger(LeaderElectionService.name);
  private client: Redis.Cluster | null = null; // Explicitly type the client as Redis.Cluster or null

  constructor(private readonly redisClientService: RedisClientService) {
    if (EnvironmentUtils.isDevelopmentMode()) {
      this.logger.log('Development environment detected. Skipping Leader Election.');
      return;
    }

    if (!EnvironmentUtils.isLeaderElectionEnabled()) {
      this.logger.log('Redis-based leader election is disabled. Skipping Leader Election.');
      return;
    }

    this.initializeRedisClient();
  }

  private initializeRedisClient() {
    try {
      this.client = this.redisClientService.getClient();

      if (!this.client) {
        this.logger.error('Redis client is unavailable. Leader election is disabled.');
      } else {
        this.logger.log('LeaderElectionService initialized successfully.');
      }
    } catch (error) {
      this.logger.error('Failed to initialize Redis client for leader election:', error);
    }
  }

  async electLeader(cronJobName: string, instanceId: string): Promise<boolean> {
    if (EnvironmentUtils.isDevelopmentMode()) {
      this.logger.log(`Development mode: Defaulting to leader for job "${cronJobName}".`);
      return true;
    }

    if (!EnvironmentUtils.isLeaderElectionEnabled()) {
      this.logger.log(`Redis leader election disabled: Defaulting to leader for job "${cronJobName}".`);
      return true;
    }

    if (!this.client) {
      this.logger.error(`Redis client unavailable for leader election. Cannot elect leader for job "${cronJobName}".`);
      return false;
    }

    const leaderKey = `leader:${cronJobName}`;
    let retryCount = 0;

    while (retryCount < 3) {
      try {
        const currentLeader = await this.client.get(leaderKey);

        if (currentLeader) {
          if (currentLeader === instanceId) {
            this.logger.log(`Job "${cronJobName}" is already running on this instance (${instanceId}).`); // If the current job not finish and next job try to start
            return false;
          } else {
            this.logger.log(`Job "${cronJobName}" is running on another instance (${currentLeader}).`);
            return false;
          }
        }

        // @ts-expect-error: TypeScript incorrectly types Redis set options for NX and EX, but the actual Redis client supports them.
        const isLeader = await this.client.set(leaderKey, instanceId, 'NX', 'EX', 30);
        if (isLeader === 'OK') {
          this.logger.log(`Instance ${instanceId} has been elected as leader for job "${cronJobName}".`);
          return true;
        }
      } catch (error) {
        this.logger.error(`Error electing leader for "${cronJobName}" (Attempt ${retryCount + 1}):`, error);
        retryCount++;
      }
    }

    this.logger.error(`Failed to elect leader for "${cronJobName}" after ${retryCount} attempts.`);
    return false;
  }

  async renewLock(cronJobName: string, currentLeader: string): Promise<void> {
    if (EnvironmentUtils.isDevelopmentMode()) {
      this.logger.log(`Development environment detected. No lock to renew for job "${cronJobName}".`);
      return;
    }

    if (!EnvironmentUtils.isLeaderElectionEnabled()) {
      this.logger.log(`Redis leader election is disabled. No lock to renew for job "${cronJobName}".`);
      return;
    }

    if (!this.client) {
      this.logger.error(`Cannot renew lock: Redis client unavailable.`);
      return;
    }

    try {
      await this.client.expire(`leader:${cronJobName}`, 30);
      this.logger.log(`Lock renewed for job "${cronJobName}" on instance (${currentLeader}).`);
    } catch (error) {
      this.logger.error(`Error renewing lock for "${cronJobName}":`, error);
    }
  }

  async releaseLock(cronJobName: string, currentLeader: string): Promise<void> {
    if (EnvironmentUtils.isDevelopmentMode()) {
      this.logger.log(`Development environment detected. No leader key to release for job "${cronJobName}".`);
      return;
    }

    if (!EnvironmentUtils.isLeaderElectionEnabled()) {
      this.logger.log(`Redis leader election is disabled. No leader key to release for job "${cronJobName}".`);
      return;
    }

    if (!this.client) {
      this.logger.error(`Cannot release lock: Redis client unavailable.`);
      return;
    }

    try {
      await this.client.del(`leader:${cronJobName}`);
      this.logger.log(`Lock released for job "${cronJobName}" on instance (${currentLeader}).`);
    } catch (error) {
      this.logger.error(`Error releasing lock for "${cronJobName}":`, error);
    }
  }

  startLockRenewal(cronJobName: string, currentLeader: string): NodeJS.Timeout | null {
    if (EnvironmentUtils.isDevelopmentMode()) {
      return null;
    }

    if (!EnvironmentUtils.isLeaderElectionEnabled()) {
      return null;
    }

    if (!this.client) {
      this.logger.error('Redis client unavailable. Lock renewal skipped.');
      return null;
    }

    this.logger.log(`Job "${cronJobName}" started on instance (${currentLeader}).`);

    return setInterval(async () => {
      try {
        await this.renewLock(cronJobName, currentLeader);
      } catch (error) {
        this.logger.error(`Failed to renew lock for job "${cronJobName}":`, error);
      }
    }, 10 * 1000); // Renew every 10 seconds
  }

  async handleDeadLetter(cronJobName: string, instanceId: string, error: Error): Promise<void> {
    // Placeholder for DLQ logic to handle failed jobs
    this.logger.error(`Error: Job "${cronJobName}" moved to Dead Letter Queue for instance ${instanceId}.`, error);
  }

  async handleJobSuccess(cronJobName: string, startTime: number, currentLeader: string) {
    const executionTime = Date.now() - startTime;
    this.logger.log(`Job "${cronJobName}" completed on instance (${currentLeader}) in ${executionTime} ms.`);
    if (executionTime > 60000) {
      this.logger.warn(`JOBS_WARNING: ${cronJobName} took more than 1 minute (${executionTime} ms).`);
    }
  }

  async checkHealth(instanceId: string): Promise<void> {
    // Placeholder for instance health checks
    this.logger.log(`Instance ${instanceId} is healthy.`);
  }

  async onModuleDestroy() {
    try {
      this.logger.log('LeaderElectionService shutting down. Releasing locks...');
      // Add any additional cleanup logic if necessary
    } catch (error) {
      this.logger.error('Error releasing locks during shutdown:', error);
    }
  }
}
