import { Body, Controller, Logger, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards';
import { LogDto } from './dto/log-error.dto';
// notes
@ApiTags('log')
@Controller('log')
export class LogController {
  private readonly logger = new Logger(LogController.name);

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('error')
  async logError(@Body() log: LogDto) {
    this.logger.error(log.message, log.stack, {
      source: 'client',
      url: log.url,
      timestamp: new Date().toISOString(),
    });
  }
}
