import { ApiProperty } from '@nestjs/swagger';
import { IGetNotificationsDto } from '@neural/models';
import { Type } from 'class-transformer';
import { IsOptional, IsNumber } from 'class-validator';

export class GetNotificationsDto implements IGetNotificationsDto {
  @ApiProperty({ description: 'Page number', type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page: number;

  @ApiProperty({ description: 'Number of items per page', type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit: number;
}
