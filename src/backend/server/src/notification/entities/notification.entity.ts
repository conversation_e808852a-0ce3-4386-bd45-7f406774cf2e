import { errorNotifications, INotification, NotificationStatus, NotificationType, NotifyStatus, UserRole } from '@neural/models';
import { Expose } from 'class-transformer';
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, Index, CreateDateColumn, DeleteDateColumn } from 'typeorm';
import { User } from '../../user/entities/user.entity';

@Entity('notifications')
@Index(['user_id', 'role'])
@Index(['user_id', 'role', 'status'])
export class Notification implements INotification {
  @PrimaryGeneratedColumn('uuid')
  notification_id!: string;

  @Column({ type: 'enum', enum: NotificationStatus, default: NotificationStatus.UNREAD })
  status: NotificationStatus;

  @Column({ type: 'enum', enum: NotificationType, nullable: false })
  type: NotificationType;

  @Column({ type: 'text', nullable: false })
  message: string;

  @Column({ type: 'text', nullable: true })
  action: string | null;

  @Column({ type: 'enum', enum: UserRole, default: null, nullable: true })
  role: UserRole | null;

  @Column({ type: 'varchar', default: null, nullable: true })
  user_id!: string | null;

  @ManyToOne(() => User, (user) => user.notifications, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deleted_at: Date | null;

  @Expose()
  get notifyStatus(): NotifyStatus {
    return errorNotifications.includes(this.type) ? NotifyStatus.DANGER : NotifyStatus.INFO;
  }
}
