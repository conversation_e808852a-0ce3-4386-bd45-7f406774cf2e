import { forwardRef, Logger, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SupabaseModule } from '../core';
import { Settings, User, UserModule } from '../user';
import { Notification } from './entities/notification.entity';
import { NotificationController } from './notification.controller';
import { NotificationGateway } from './services/notification.gateway';
import { NotificationService } from './services/notification.service';
import { NotifyService } from './services/notify.service';

@Module({
  imports: [TypeOrmModule.forFeature([User, Notification, Settings]), SupabaseModule, forwardRef(() => UserModule)],
  controllers: [NotificationController],
  providers: [NotificationGateway, NotificationService, NotifyService, Logger],
  exports: [NotificationGateway, NotificationService, NotifyService],
})
export class NotificationModule {}
