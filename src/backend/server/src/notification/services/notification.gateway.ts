import { Logger } from '@nestjs/common';
import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { IGpusRented, INotification, IPayment, WsEvent } from '@neural/models';
import { createAdapter } from '@socket.io/redis-adapter';
import Redis from 'ioredis';
import { Server, Socket } from 'socket.io';
import { SupabaseService } from '../../core';
import { EnvironmentUtils } from '../../core/utils';

@WebSocketGateway({
  path: '/ws',
  cors: true,
})
export class NotificationGateway {
  @WebSocketServer()
  server: Server;
  private readonly logger = new Logger(NotificationGateway.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  // OnGatewayInit
  afterInit() {
    // authentication middleware
    this.server.use(async (socket: Socket, next: (err?: Error) => void) => {
      const token = socket.handshake.auth?.token;
      if (token) {
        try {
          const { email, role, user_id } = await this.supabaseService.verifyToken(token);
          if (user_id) {
            socket.data.email = email;
            socket.data.role = role;
            socket.data.user_id = user_id;
            return next();
          }
        } catch (error) {
          return next(new Error('Unauthorized'));
        }
      }
      return next(new Error('Unauthorized'));
    });

    // Add the Redis adapter
    if (!EnvironmentUtils.isDevelopmentMode() && EnvironmentUtils.isLeaderElectionEnabled()) {
      try {
        const redisOptions = {
          host: process.env.REDIS_HOST_IP,
          port: parseInt(process.env.REDIS_HOST_PORT as string, 10),
        };

        const pubClient = new Redis.Cluster([redisOptions]);
        const subClient = pubClient.duplicate();

        pubClient.on('connect', () => this.logger.log('Redis pubClient connected.'));
        pubClient.on('error', (err) => this.logger.error('Redis pubClient error:', err));

        subClient.on('connect', () => this.logger.log('Redis subClient connected.'));
        subClient.on('error', (err) => this.logger.error('Redis subClient error:', err));

        this.server.adapter(createAdapter(pubClient, subClient));
        this.logger.log('Socket.IO Redis adapter initialized successfully.');
      } catch (error) {
        this.logger.error('Error initializing Redis adapter:', error);
      }
    }
  }

  // OnGatewayConnection
  handleConnection(client: Socket) {
    client.join(client.data.user_id); // join user_id room
    client.join(client.data.role); // join role room
  }

  // OnGatewayDisconnect
  handleDisconnect(client: Socket) {
    client.leave(client.data.user_id); // leave user_id room
    client.leave(client.data.role); // leave role room
  }

  /**
   * API methods
   */

  newNotification(socket_room: string, notification: INotification) {
    this.server.to(socket_room).emit(WsEvent.NEW_NOTIFICATION, notification);
  }

  updateBalance(user_id: string, balance: number) {
    this.server.to(user_id).emit(WsEvent.UPDATE_BALANCE, balance);
  }

  removeDeallocatedGpu(user_id: string, gpu_rented_id: string) {
    this.server.to(user_id).emit(WsEvent.GPU_DEALLOCATED, gpu_rented_id);
  }

  updateCurrentCost(user_id: string, cost: number) {
    this.server.to(user_id).emit(WsEvent.UPDATE_CURRENT_COST, cost);
  }

  newPayment(user_id: string, payment: IPayment) {
    this.server.to(user_id).emit(WsEvent.NEW_PAYMENT, payment);
  }

  updateRentedGpu(user_id: string, rentedGpu: IGpusRented) {
    this.server.to(user_id).emit(WsEvent.UPDATE_RENTED_GPU, rentedGpu);
  }

  updateCurrentRunway(user_id: string, currentRunway: number) {
    this.server.to(user_id).emit(WsEvent.UPDATE_CURRENT_RUNWAY, currentRunway);
  }

  updateAutoTopupActive(user_id: string, auto_topup_active: boolean) {
    this.server.to(user_id).emit(WsEvent.UPDATE_AUTO_TOPUP_ACTIVE, auto_topup_active);
  }
}
