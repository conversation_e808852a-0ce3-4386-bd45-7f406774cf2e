import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { INotification, IPaginatedNotification, NotificationStatus, UserRole } from '@neural/models';
import { IsNull, Repository } from 'typeorm';
import { Notification } from '../entities/notification.entity';
import { NotificationUtils } from '../utils';

@Injectable()
export class NotificationService {
  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
  ) {}

  async getNotifications(user_id: string, role: UserRole, limit: number, page: number): Promise<IPaginatedNotification> {
    const [notifications, count] = await this.notificationRepository
      .createQueryBuilder('notification')
      .where(
        '(notification.user_id = :user_id AND notification.role IS NULL AND notification.status != :archived) OR ' +
          '(notification.user_id IS NULL AND notification.role = :role AND notification.status != :archived)',
        { user_id, role, archived: NotificationStatus.ARCHIVED },
      )
      .andWhere('notification.deleted_at IS NULL')
      .orderBy('notification.created_at', 'DESC')
      .take(limit)
      .skip((page - 1) * limit)
      .getManyAndCount();

    const serializedNotifications = NotificationUtils.addNotifyStatusProperty(notifications); // add the computed notifyStatus property ot Notification object
    return {
      notifications: serializedNotifications as INotification[],
      page,
      limit,
      has_more: count > page * limit,
    };
  }

  async getCount(user_id: string, role: UserRole, status: NotificationStatus) {
    return await this.notificationRepository.count({
      where: [
        { user_id, role: IsNull(), status },
        { user_id: IsNull(), role, status },
      ],
    });
  }

  async getNotificationsByStatus(user_id: string, role: UserRole, status: NotificationStatus) {
    return await this.notificationRepository.find({
      where: [
        { user_id, role: IsNull(), status },
        { user_id: IsNull(), role, status },
      ],
      take: 10, // TODO implement pagination
      order: { created_at: 'DESC' },
    });
  }

  async archiveNotification(notification_id: string): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({ where: { notification_id } });
    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    notification.status = NotificationStatus.ARCHIVED;

    return await this.notificationRepository.save(notification);
  }

  async deleteNotification(notification_id: string, user_id: string): Promise<void> {
    const notification = await this.notificationRepository.findOne({ where: { notification_id, user_id } });

    if (!notification) {
      throw new NotFoundException('Notification not found or does not belong to the user');
    }

    await this.notificationRepository.delete(notification_id);
  }

  async deleteAllNotificationsForUser(user_id: string): Promise<void> {
    const result = await this.notificationRepository.delete({ user_id });

    if (result.affected === 0) {
      throw new NotFoundException('No notifications found for the user');
    }
  }
}
