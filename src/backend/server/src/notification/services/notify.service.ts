import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { INotification, NotificationType, UserRole, isNotificationOn } from '@neural/models';
import { QueryRunner, Repository } from 'typeorm';
import { Settings } from '../../user';
import { User } from '../../user/entities/user.entity';
import { Notification } from '../entities/notification.entity';
import { NotificationUtils } from '../utils';
import { NotificationGateway } from './notification.gateway';

@Injectable()
export class NotifyService {
  private readonly logger = new Logger(NotifyService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    @InjectRepository(Settings)
    readonly userSettingsRepository: Repository<Settings>,
    private readonly notificationGateway: NotificationGateway,
  ) {}

  async notifyUser(user_id: string, type: NotificationType, message: string, queryRunner?: QueryRunner) {
    const settings = await (queryRunner
      ? queryRunner.manager.findOne(Settings, {
          where: {
            user_id,
          },
        })
      : this.userSettingsRepository.findOne({
          where: {
            user_id,
          },
        }));

    if (settings && !isNotificationOn(settings, type)) {
      return;
    }

    const notification = this.notificationRepository.create({ type, message, user_id });

    this.saveAndNotify(user_id, notification, queryRunner);
  }

  async notifyUserWithoutCheckingSettings(user_id: string, type: NotificationType, message: string) {
    const notification = this.notificationRepository.create({ type, message, user_id });

    this.saveAndNotify(user_id, notification);
  }

  async notifyRole(role: UserRole, type: NotificationType, message: string) {
    const notification = this.notificationRepository.create({ type, message, role });

    this.saveAndNotify(role, notification);
  }

  async notifyPaymentSucceeded(user_id: string, message: string, receiptUrl: string | null) {
    const user = await this.userRepository.findOne({ where: { user_id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const notification = this.notificationRepository.create({
      type: NotificationType.PAYMENT_SUCCEEDED,
      message,
      action: receiptUrl,
      user_id,
    });

    this.saveAndNotify(user_id, notification);
  }

  async notifyAutoPaymentPending(user_id: string, message: string, authorizationLink: string) {
    const notification = this.notificationRepository.create({
      type: NotificationType.PAYMENT_PENDING,
      message,
      action: authorizationLink,
      user_id,
    });

    this.saveAndNotify(user_id, notification);
  }

  async notifyPaymentPending(user_id: string, message: string) {
    try {
      const notification = this.notificationRepository.create({
        type: NotificationType.PAYMENT_PENDING,
        message,
        user_id,
      });

      this.saveAndNotify(user_id, notification);
    } catch (error) {
      this.logger.error(`Error saving notification: ${error.message}`);
    }
  }

  async notifyFreeCreditsGiven(user_id: string, message: string) {
    const notification = this.notificationRepository.create({
      type: NotificationType.FREE_CREDITS_GIVEN,
      message,
      user_id,
    });

    this.saveAndNotify(user_id, notification);
  }

  async notifyPaymentFailed(user_id: string, errorMessage: string) {
    const user = await this.userRepository.findOne({ where: { user_id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const notification = this.notificationRepository.create({
      type: NotificationType.PAYMENT_FAILED,
      message: errorMessage,
      user_id,
    });

    this.saveAndNotify(user_id, notification);
  }

  private async saveAndNotify(socket_room: string, notification: Notification, queryRunner?: QueryRunner) {
    try {
      const saved = await (queryRunner ? queryRunner.manager.save(notification) : this.notificationRepository.save(notification));

      const updatedNotification = NotificationUtils.addNotifyStatusProperty(saved); // add the computed notifyStatus property ot Notification object
      this.notificationGateway.newNotification(socket_room, updatedNotification as INotification);
    } catch (error) {
      this.logger.error(`Error saving notification: ${error.message}`);
    }
  }

  async notifyHasSshKey(user_id: string, message: string, pageLink: string) {
    const notification = this.notificationRepository.create({
      type: NotificationType.SSH_KEY_CREATED,
      message,
      action: pageLink,
      user_id,
    });

    this.saveAndNotify(user_id, notification);
  }

  async notifyGpuDeployedWithoutSshKey(user_id: string, message: string, pageLink: string) {
    const notification = this.notificationRepository.create({
      type: NotificationType.GPU_DEPLOYED_WITHOUT_SSH_KEY,
      message,
      action: pageLink,
      user_id,
    });

    this.saveAndNotify(user_id, notification);
  }
}
