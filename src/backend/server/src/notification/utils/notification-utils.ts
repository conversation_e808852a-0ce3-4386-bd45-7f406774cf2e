import { INotification } from '@neural/models';
import { instanceTo<PERSON>lain } from 'class-transformer';
import { Notification } from '../entities/notification.entity';

export class NotificationUtils {
  static addNotifyStatusProperty(notification: Notification | Notification[]): INotification | INotification[] {
    // Serialize the Notification instance to include notifyStatus computed property
    const serializedNotification = instanceToPlain(notification);
    return serializedNotification as INotification | INotification[];
  }
}
