# GCP Pub/Sub Integration

This module provides integration with Google Cloud Pub/Sub for publishing and subscribing to GPU-related events.

## Features

- **Publisher Service**: Publishes deallocation events to a Pub/Sub topic
- **Subscriber Service**: Long-running service that subscribes to Pub/Sub messages and logs them
- **Webhook Integration**: GPU webhook automatically publishes deallocation events to Pub/Sub

## Configuration

Add the following environment variables to enable Pub/Sub:

```bash
# Enable/disable Pub/Sub functionality
PUBSUB_ENABLED=true

# GCP Project ID
GCP_PROJECT_ID=your-gcp-project-id

# Pub/Sub topic and subscription names
PUBSUB_TOPIC_NAME=gpu-events
PUBSUB_SUBSCRIPTION_NAME=gpu-events-subscription

# Optional: Path to service account key file
# If not provided, will use default GCP credentials
GCP_KEY_FILENAME=/path/to/service-account-key.json
```

## Setup

1. **Install Dependencies** (when ready to use):
   ```bash
   npm install @google-cloud/pubsub
   ```

2. **GCP Setup**:
   - Create a GCP project
   - Enable the Pub/Sub API
   - Create a service account with Pub/Sub permissions
   - Download the service account key file (optional if using default credentials)

3. **Topic and Subscription**:
   - The topic and subscription will be created automatically if they don't exist
   - Or create them manually in the GCP Console

## Usage

### Publishing Events

The `PubSubService` automatically publishes deallocation events when the GPU webhook receives them:

```typescript
await this.pubSubService.publishDeallocationEvent({
  eventType: 'deallocation',
  timestamp: new Date().toISOString(),
  data: {
    hotkey: 'gpu-hotkey',
    uuid: 'allocation-uuid',
    deallocated_at: '2023-01-01T00:00:00.000Z',
    gpu_rented_id: 'gpu-rented-id',
    user_id: 'user-id',
  },
});
```

### Subscribing to Events

The `PubSubSubscriberService` runs as a long-running background service that:
- Automatically starts when the application starts
- Listens for messages on the configured subscription
- Logs received messages
- Can be extended to perform additional processing

## Message Format

Deallocation events are published with the following structure:

```typescript
interface DeallocationEventMessage {
  eventType: 'deallocation';
  timestamp: string; // ISO 8601 timestamp
  data: {
    hotkey: string;
    uuid: string;
    deallocated_at: string; // ISO 8601 timestamp
    gpu_rented_id?: string;
    user_id?: string;
  };
}
```

## Development Mode

When `PUBSUB_ENABLED=false` or the environment variable is not set:
- Services will log that Pub/Sub is disabled
- No actual Pub/Sub operations will be performed
- The application will continue to work normally

## Error Handling

- Pub/Sub failures do not affect the main webhook functionality
- Errors are logged but do not cause the webhook to fail
- The subscriber service handles message processing errors gracefully

## Monitoring

The services provide detailed logging for:
- Connection status
- Message publishing
- Message receiving
- Error conditions

Check the application logs for Pub/Sub-related activity.
