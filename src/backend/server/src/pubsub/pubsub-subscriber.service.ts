import { Injectable, Logger, OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PubSub, Subscription, Message } from '@google-cloud/pubsub';
import { DeallocationEventMessage } from './pubsub.service';

@Injectable()
export class PubSubSubscriberService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PubSubSubscriberService.name);
  // private pubSubClient: PubSub | null = null;
  // private subscription: Subscription | null = null;
  private readonly enabled: boolean;
  private readonly projectId: string;
  private readonly topicName: string;
  private readonly subscriptionName: string;
  private isListening = false;

  constructor(private readonly configService: ConfigService) {
    this.enabled = this.configService.get<boolean>('pubsub.enabled', false);
    this.projectId = this.configService.get<string>('pubsub.projectId');
    this.topicName = this.configService.get<string>('pubsub.topicName');
    this.subscriptionName = this.configService.get<string>('pubsub.subscriptionName');
  }

  async onModuleInit() {
    if (this.enabled) {
      await this.initializeSubscriber();
      await this.startListening();
    } else {
      this.logger.log('Pub/Sub subscriber is disabled. Set PUBSUB_ENABLED=true to enable.');
    }
  }

  private async initializeSubscriber() {
    try {
      if (!this.projectId) {
        throw new Error('GCP_PROJECT_ID is required when Pub/Sub is enabled');
      }

      // Uncomment when @google-cloud/pubsub is installed
      // const keyFilename = this.configService.get<string>('pubsub.keyFilename');
      // this.pubSubClient = new PubSub({
      //   projectId: this.projectId,
      //   ...(keyFilename && { keyFilename }),
      // });

      // const topic = this.pubSubClient.topic(this.topicName);
      // this.subscription = topic.subscription(this.subscriptionName);

      // // Check if subscription exists, create if it doesn't
      // const [exists] = await this.subscription.exists();
      // if (!exists) {
      //   await this.subscription.create({
      //     ackDeadlineSeconds: 60,
      //     messageRetentionDuration: { seconds: 604800 }, // 7 days
      //   });
      //   this.logger.log(`Created Pub/Sub subscription: ${this.subscriptionName}`);
      // }

      this.logger.log(`Pub/Sub subscriber initialized for subscription: ${this.subscriptionName}`);
    } catch (error) {
      this.logger.error('Failed to initialize Pub/Sub subscriber:', error);
      throw error;
    }
  }

  private async startListening() {
    if (!this.enabled) {
      return;
    }

    try {
      // if (!this.subscription) {
      //   throw new Error('Pub/Sub subscription is not initialized');
      // }

      this.isListening = true;
      this.logger.log(`Starting to listen for messages on subscription: ${this.subscriptionName}`);

      // Uncomment when @google-cloud/pubsub is installed
      // this.subscription.on('message', this.handleMessage.bind(this));
      // this.subscription.on('error', this.handleError.bind(this));

      // For now, simulate listening
      this.logger.log('Pub/Sub subscriber is now listening for messages...');
      
      // Simulate periodic logging to show it's working
      setInterval(() => {
        if (this.isListening) {
          this.logger.debug('Pub/Sub subscriber is actively listening for messages');
        }
      }, 60000); // Log every minute
    } catch (error) {
      this.logger.error('Failed to start listening for Pub/Sub messages:', error);
      throw error;
    }
  }

  // private handleMessage(message: Message) {
  //   try {
  //     const messageData = message.data.toString();
  //     const parsedMessage: DeallocationEventMessage = JSON.parse(messageData);
      
  //     this.logger.log('Received Pub/Sub message:', {
  //       messageId: message.id,
  //       eventType: parsedMessage.eventType,
  //       timestamp: parsedMessage.timestamp,
  //       data: parsedMessage.data,
  //     });

  //     // Process the message based on event type
  //     if (parsedMessage.eventType === 'deallocation') {
  //       this.handleDeallocationEvent(parsedMessage);
  //     }

  //     // Acknowledge the message
  //     message.ack();
  //   } catch (error) {
  //     this.logger.error('Error processing Pub/Sub message:', error);
  //     // Don't acknowledge the message so it can be retried
  //     message.nack();
  //   }
  // }

  private handleDeallocationEvent(message: DeallocationEventMessage) {
    this.logger.log('Processing deallocation event from Pub/Sub:', {
      hotkey: message.data.hotkey,
      uuid: message.data.uuid,
      deallocated_at: message.data.deallocated_at,
      gpu_rented_id: message.data.gpu_rented_id,
      user_id: message.data.user_id,
    });

    // Here you can add any additional processing logic for deallocation events
    // For example, updating analytics, sending notifications, etc.
  }

  // private handleError(error: Error) {
  //   this.logger.error('Pub/Sub subscription error:', error);
  // }

  async stopListening() {
    if (this.isListening) {
      this.isListening = false;
      // if (this.subscription) {
      //   this.subscription.removeAllListeners();
      // }
      this.logger.log('Stopped listening for Pub/Sub messages');
    }
  }

  async onModuleDestroy() {
    if (this.enabled) {
      try {
        await this.stopListening();
        // if (this.pubSubClient) {
        //   await this.pubSubClient.close();
        //   this.logger.log('Pub/Sub subscriber client closed');
        // }
        this.logger.log('Pub/Sub subscriber service destroyed');
      } catch (error) {
        this.logger.error('Error closing Pub/Sub subscriber:', error);
      }
    }
  }
}
