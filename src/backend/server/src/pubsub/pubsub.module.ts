import { Lo<PERSON>, Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PubSubService } from './pubsub.service';
import { PubSubSubscriberService } from './pubsub-subscriber.service';
import pubsubConfig from '../core/configs/pubsub.config';

@Module({
  imports: [
    ConfigModule.forFeature(pubsubConfig),
  ],
  providers: [
    PubSubService,
    PubSubSubscriberService,
    Logger,
  ],
  exports: [
    PubSubService,
    PubSubSubscriberService,
  ],
})
export class PubSubModule {}
