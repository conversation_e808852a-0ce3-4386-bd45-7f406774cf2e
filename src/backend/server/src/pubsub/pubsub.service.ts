import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PubSub, Topic } from '@google-cloud/pubsub';

export interface DeallocationEventMessage {
  eventType: 'deallocation';
  timestamp: string;
  data: {
    hotkey: string;
    uuid: string;
    deallocated_at: string;
    gpu_rented_id?: string;
    user_id?: string;
  };
}

@Injectable()
export class PubSubService implements OnModuleDestroy {
  private readonly logger = new Logger(PubSubService.name);
  private pubSubClient: PubSub | null = null;
  private topic: Topic | null = null;
  private readonly enabled: boolean;
  private readonly projectId: string;
  private readonly topicName: string;

  constructor(private readonly configService: ConfigService) {
    this.enabled = this.configService.get<boolean>('pubsub.enabled', false);
    this.projectId = this.configService.get<string>('pubsub.projectId');
    this.topicName = this.configService.get<string>('pubsub.topicName');

    if (this.enabled) {
      this.initializePubSub();
    } else {
      this.logger.log('Pub/Sub is disabled. Set PUBSUB_ENABLED=true to enable.');
    }
  }

  private async initializePubSub() {
    try {
      if (!this.projectId) {
        throw new Error('GCP_PROJECT_ID is required when Pub/Sub is enabled');
      }

      const keyFilename = this.configService.get<string>('pubsub.keyFilename');
      this.pubSubClient = new PubSub({
        projectId: this.projectId,
        ...(keyFilename && { keyFilename }),
      });

      this.topic = this.pubSubClient.topic(this.topicName);

      // Check if topic exists, create if it doesn't
      const [exists] = await this.topic.exists();
      if (!exists) {
        await this.topic.create();
        this.logger.log(`Created Pub/Sub topic: ${this.topicName}`);
      }

      this.logger.log(`Pub/Sub initialized for project: ${this.projectId}, topic: ${this.topicName}`);
    } catch (error) {
      this.logger.error('Failed to initialize Pub/Sub:', error);
      throw error;
    }
  }

  async publishDeallocationEvent(message: DeallocationEventMessage): Promise<void> {
    if (!this.enabled) {
      this.logger.debug('Pub/Sub is disabled, skipping message publication');
      return;
    }

    try {
      if (!this.topic) {
        throw new Error('Pub/Sub topic is not initialized');
      }

      const messageData = Buffer.from(JSON.stringify(message));

      const messageId = await this.topic.publishMessage({
        data: messageData,
        attributes: {
          eventType: message.eventType,
          timestamp: message.timestamp,
        },
      });

      this.logger.log(`Published deallocation event with message ID: ${messageId}`);
    } catch (error) {
      this.logger.error('Failed to publish deallocation event:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    if (this.enabled) {
      try {
        if (this.pubSubClient) {
          await this.pubSubClient.close();
          this.logger.log('Pub/Sub client closed');
        }
        this.logger.log('Pub/Sub service destroyed');
      } catch (error) {
        this.logger.error('Error closing Pub/Sub client:', error);
      }
    }
  }
}
