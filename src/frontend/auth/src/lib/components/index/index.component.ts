import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { NbButtonModule } from '@nebular/theme';
import { UIStore } from '@neural/components';

@Component({
  selector: 'ni-auth-index',
  standalone: true,
  imports: [RouterLink, NbButtonModule],
  templateUrl: './index.component.html',
  styleUrl: './index.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IndexComponent implements OnInit {
  ui = inject(UIStore);
  route = inject(ActivatedRoute);

  ngOnInit() {
    if (this.route.snapshot.fragment) {
      const errorMsg = new URLSearchParams(this.route.snapshot.fragment).get('error_description');
      errorMsg ? this.ui.showError(errorMsg) : null;
    }
  }
}
