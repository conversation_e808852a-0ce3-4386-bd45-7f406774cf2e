<h2 class="title">Log in</h2>

@if (!withPass()) {

<div class="auth-wrapper">
  <button nbButton class="status-neural" status="basic" shape="semi-round" (click)="session.signInWithOAuth('google')">
    <nb-icon icon="google" pack="oauth" />Continue with Google
  </button>
  <button nbButton class="status-neural" status="basic" shape="semi-round" (click)="session.signInWithOAuth('github')">
    <nb-icon icon="github" pack="oauth" />Continue with GitHub
  </button>

  <a class="auth-with-email" nbButton (click)="toggleForm()">Continue with email <nb-icon icon="arrow_forward" /></a>
  <a class="auth-forgot-pass" nbButton routerLink="../reset">Forgot password</a>

  <button class="auth-go-signup" nbButton status="primary" shape="round" size="medium" outline routerLink="../signup">
    Create account
  </button>
</div>

} @else {

<form class="auth-wrapper" [formGroup]="form" (ngSubmit)="doSubmit()">
  <input
    placeholder="Enter email address"
    formControlName="email"
    #autofocus
    nbInput
    type="text"
    shape="semi-round"
    [status]="form.controls.email.dirty ? (!form.controls.email.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.email.touched && !form.controls.email.valid ? true : null"
  />
  @if (form.controls.email.touched && !form.controls.email.valid) { @if (form.controls.email.errors?.required) {
  <span class="caption status-danger"> Email is required </span>
  } @if (form.controls.email.errors?.email) {
  <span class="caption status-danger"> Email is invalid </span>
  } }

  <input
    formControlName="password"
    placeholder="Enter password"
    nbInput
    type="password"
    shape="semi-round"
    [status]="form.controls.password.dirty ? (!form.controls.password.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.password.touched && !form.controls.password.valid ? true : null"
  />
  <div>
    <span class="caption status-danger">
      {{
        form.controls.password.touched && form.controls.password.errors?.required
          ? PASSWORD_EMPTY_MESSAGE_LOGIN
          : form.controls.password.touched && form.controls.password.errors
          ? PASSWORD_VALIDATION_MESSAGE
          : session.errorMsg()
      }}
    </span>
  </div>

  <a nbButton routerLink="../reset">Forgot password</a>

  <button type="submit" nbButton status="primary" shape="round" size="medium">Log in</button>

  <a nbButton (click)="toggleForm()"><nb-icon icon="arrow_back" /> Back</a>
</form>

}
