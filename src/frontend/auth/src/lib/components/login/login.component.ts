import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, ElementRef, effect, inject, model, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractControl, FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { NbButtonModule, NbIconModule, NbInputModule } from '@nebular/theme';
import { take } from 'rxjs/operators';
import { PASSWORD_EMPTY_MESSAGE_LOGIN, passwordRegex, PASSWORD_VALIDATION_MESSAGE } from '../../constants';
import { SessionStore } from '../../store/session.store';

@Component({
  selector: 'ni-auth-login',
  standalone: true,
  imports: [ReactiveFormsModule, RouterLink, NbButtonModule, NbIconModule, NbInputModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'auth-component' },
})
export class LoginComponent {
  input = viewChild<ElementRef<HTMLInputElement>>('autofocus');

  withPass = model(false); // coming as url queryParam too

  session = inject(SessionStore);
  destroyRef = inject(DestroyRef);

  readonly form = inject(FormBuilder).nonNullable.group(
    {
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.pattern(passwordRegex), () => (this.session.errorMsg() ? { invalid: true } : null)]],
    },
    {
      updateOn: 'change',
    },
  );

  readonly PASSWORD_EMPTY_MESSAGE_LOGIN = PASSWORD_EMPTY_MESSAGE_LOGIN;
  readonly PASSWORD_VALIDATION_MESSAGE = PASSWORD_VALIDATION_MESSAGE;

  constructor() {
    effect(() => {
      this.input()?.nativeElement.focus();
    });
  }

  toggleForm() {
    this.withPass.set(!this.withPass());
  }

  async doSubmit() {
    if (this.form.valid) {
      const { error } = await this.session.signIn(this.form.getRawValue());
      if (error) {
        this.form.updateValueAndValidity();
        this.form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef), take(1)).subscribe(() => {
          this.session.resetErrorMsg();
        });
      } else {
        setTimeout(() => {
          window.location.href = '/';
        }, 0);
      }
    }
  }
}
