import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { NbFocusMonitor, NbStatusService } from '@nebular/theme';
import { provideNoopSessionStore } from '@neural/auth/testing';
import { provideNoopUIStore } from '@neural/components/testing';
import { provideNoopAuthService } from '@neural/query/testing';
import { MockProvider } from 'ng-mocks';
import { ReCaptchaV3Service } from 'ng-recaptcha-2';
import { of } from 'rxjs';
import { RedirectComponent } from './redirect.component';

describe('RedirectComponent', () => {
  let component: RedirectComponent;
  let fixture: ComponentFixture<RedirectComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RedirectComponent],
      providers: [
        provideRouter([]),
        MockProvider(NbFocusMonitor, { monitor: () => of(null) }),
        MockProvider(NbStatusService),
        provideNoopSessionStore(),
        provideNoopAuthService(),
        provideNoopUIStore(),
        MockProvider(ReCaptchaV3Service),
      ],
    });
    fixture = TestBed.createComponent(RedirectComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
