import { ChangeDetectionStrategy, Component, inject, OnDestroy, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { NbButtonModule } from '@nebular/theme';
import { UIStore } from '@neural/components';
import { Subject } from 'rxjs';
import { SessionStore, VerifyTokenHashParams } from '../../store';

@Component({
  selector: 'ni-auth-redirect',
  standalone: true,
  imports: [NbButtonModule, RouterLink],
  templateUrl: './redirect.component.html',
  styleUrl: './redirect.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RedirectComponent implements OnInit, OnDestroy {
  ui = inject(UIStore);
  session = inject(SessionStore);
  tokenHash: string | null = null;
  type: 'signup' | 'invite' | 'magiclink' | 'recovery' | 'email_change' | 'email' | null = null;
  redirectTo: string | null = null;
  hasError = signal(false);
  private destroy$ = new Subject<void>();

  constructor(private route: ActivatedRoute, private router: Router) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.tokenHash = params['token'];
      this.type = params['type'];
      this.redirectTo = params['redirect_to'];

      if (!this.tokenHash || !this.type) {
        this.router.navigate(['/auth/reset']);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async confirmAction(params: VerifyTokenHashParams): Promise<void> {
    try {
      const { error } = await this.session.verifyOtpWhitTokenHash({
        token_hash: params.token_hash,
        type: params.type,
      });
      if (!error) {
        this.router.navigate(['/auth/resetting']);
      } else {
        this.hasError.set(true);
      }
    } catch (err) {
      this.ui.showError('Error confirming action');
      this.hasError.set(true);
    }
  }

  onContinueToReset(): void {
    if (this.tokenHash && this.type) {
      this.confirmAction({ token_hash: this.tokenHash, type: this.type });
    }
  }
}
