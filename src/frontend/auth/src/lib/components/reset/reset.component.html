<h2 class="title">Forgot Password</h2>

<form class="auth-wrapper" [formGroup]="form" (ngSubmit)="doSubmit()">
  <input
    formControlName="email"
    placeholder="Enter email address"
    #autofocus
    nbInput
    type="text"
    shape="semi-round"
    [status]="form.controls.email.touched ? (!form.controls.email.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.email.touched && !form.controls.email.valid ? true : null"
  />

  @if (!errorMsg() && !successMsg()) {
  <p>If there is an account registered with this email, we'll send you a new password, which you can then change in your settings.</p>
  } @else if (successMsg()) {
  <span class="caption status-success">{{ successMsg() }}</span>
  } @else if (errorMsg()) {
  <span class="caption status-danger">{{ errorMsg() }}</span>
  }

  <button [disabled]="successMsg() || !form.valid" type="submit" nbButton shape="round" size="medium" status="primary">Submit</button>

  <a nbButton (click)="goBack()"><nb-icon icon="arrow_back" /> Back</a>
</form>
