import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { NbFocusMonitor, NbStatusService } from '@nebular/theme';
import { provideNoopSessionStore } from '@neural/auth/testing';
import { IconsModule } from '@neural/components';
import { AuthService } from '@neural/query';
import { MockProvider } from 'ng-mocks';
import { of } from 'rxjs';
import { UserEmailService } from '../../services';
import { ResetComponent } from './reset.component';

describe('ResetComponent', () => {
  let component: ResetComponent;
  let fixture: ComponentFixture<ResetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ResetComponent, IconsModule],
      providers: [
        provideRouter([]),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(NbFocusMonitor, { monitor: () => of(null) }),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(NbStatusService),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(AuthService),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(UserEmailService),
        provideNoopSessionStore(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ResetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
