import { Location } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, ElementRef, OnInit, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { NbButtonModule, NbIconModule, NbInputModule } from '@nebular/theme';
import { UserEmailService } from '../../services';
import { SessionStore } from '../../store/session.store';

@Component({
  selector: 'ni-auth-reset',
  standalone: true,
  imports: [ReactiveFormsModule, NbButtonModule, NbIconModule, NbInputModule],
  templateUrl: './reset.component.html',
  styleUrl: './reset.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'auth-component' },
  providers: [UserEmailService],
})
export class ResetComponent implements OnInit {
  input = viewChild<ElementRef<HTMLInputElement>>('autofocus');

  location = inject(Location);
  session = inject(SessionStore);
  destroyRef = inject(DestroyRef);
  userEmailService = inject(UserEmailService);

  errorMsg = signal<string | null>(null);
  successMsg = signal<string | null>(null);

  readonly form = inject(FormBuilder).nonNullable.group(
    {
      email: ['', [Validators.required, Validators.email]],
    },
    {
      updateOn: 'change',
    },
  );

  ngOnInit() {
    this.input()?.nativeElement.focus();

    this.form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.errorMsg.set('');
      this.successMsg.set('');
    });
  }

  goBack() {
    this.location.back();
    return false;
  }

  async doSubmit() {
    if (this.form.valid) {
      const { exists, error: errorMessage } = await this.userEmailService.validateEmailExists(this.form.getRawValue().email);

      if (errorMessage) {
        this.errorMsg.set(errorMessage);
        return;
      }

      if (!exists) {
        this.errorMsg.set('User with this email does not exist');
        return;
      }
      const { error } = await this.session.resetPassword(this.form.getRawValue().email);
      if (error) {
        this.errorMsg.set(error.message);
      } else {
        this.errorMsg.set('');
        this.successMsg.set('Password reset email sent');
      }
    }
  }
}
