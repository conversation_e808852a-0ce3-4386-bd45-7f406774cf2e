<h2 class="title">Update Password</h2>

<form class="auth-wrapper" [formGroup]="form" (ngSubmit)="doSubmit()">
  <input
    formControlName="password"
    placeholder="Enter new password"
    #autofocus
    nbInput
    type="password"
    shape="semi-round"
    [status]="form.controls.password.touched && form.controls.password.invalid ? 'danger' : 'basic'"
    [attr.aria-invalid]="form.controls.password.touched && form.controls.password.invalid ? true : null"
  />

  @if (form.controls.password.touched) { @if (form.controls.password.errors?.required) {
  <span class="caption status-danger">Password is required</span>
  } @else if (!form.controls.password.valid) {
  <span class="caption status-danger">{{ PASSWORD_VALIDATION_MESSAGE }}</span>
  } }

  <input
    formControlName="passwordConfirm"
    placeholder="Confirm new password"
    nbInput
    type="password"
    shape="semi-round"
    [status]="form.controls.passwordConfirm.touched && form.controls.passwordConfirm.invalid ? 'danger' : 'basic'"
    [attr.aria-invalid]="form.controls.passwordConfirm.touched && form.controls.passwordConfirm.invalid ? true : null"
  />

  @if (form.controls.passwordConfirm.touched) { @if (form.controls.passwordConfirm.errors?.required) {
  <span class="caption status-danger">Confirm password is required</span>
  } @else if (form.errors?.passwordsMismatch) {
  <span class="caption status-danger">Passwords must match</span>
  } } @if (errorMsg() && !form.controls.password.errors && !form.controls.passwordConfirm.errors && !form.errors?.passwordsMismatch) {
  <span class="caption status-danger">{{ errorMsg() }}</span>
  }

  <button type="submit" [disabled]="form.invalid" nbButton status="primary" shape="round" size="medium">Submit</button>
</form>
