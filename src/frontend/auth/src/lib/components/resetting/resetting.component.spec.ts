import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { NbFocusMonitor, NbStatusService } from '@nebular/theme';
import { provideNoopSessionStore } from '@neural/auth/testing';
import { provideNoopUIStore } from '@neural/components/testing';
import { MockProvider } from 'ng-mocks';
import { of } from 'rxjs';
import { ResettingComponent } from './resetting.component';

describe('ResettingComponent', () => {
  let component: ResettingComponent;
  let fixture: ComponentFixture<ResettingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ResettingComponent],
      providers: [
        provideRouter([]),
        MockProvider(NbFocusMonitor, { monitor: () => of(null) }),
        MockProvider(NbStatusService),
        provideNoopSessionStore(),
        provideNoopUIStore(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ResettingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
