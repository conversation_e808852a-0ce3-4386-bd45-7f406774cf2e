import { ChangeDetectionStrategy, Component, ElementRef, OnInit, inject, signal, viewChild } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NbButtonModule, NbInputModule } from '@nebular/theme';
import { UIStore } from '@neural/components';
import { PASSWORD_VALIDATION_MESSAGE, passwordRegex } from '../../constants';
import { SessionStore } from '../../store/session.store';
import { passwordsMismatch } from '../../validators';

@Component({
  selector: 'ni-auth-resetting',
  standalone: true,
  imports: [ReactiveFormsModule, NbButtonModule, NbInputModule],
  templateUrl: './resetting.component.html',
  styleUrl: './resetting.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'auth-component' },
})
export class ResettingComponent implements OnInit {
  input = viewChild<ElementRef<HTMLInputElement>>('autofocus');

  router = inject(Router);
  session = inject(SessionStore);
  ui = inject(UIStore);

  errorMsg = signal<string | null>(null);
  successMsg = signal<string | null>(null);

  readonly form = inject(FormBuilder).nonNullable.group(
    {
      password: ['', [Validators.required, Validators.pattern(passwordRegex)]],
      passwordConfirm: ['', [Validators.required]],
    },
    {
      updateOn: 'change',
      validators: [passwordsMismatch],
    },
  );

  readonly PASSWORD_VALIDATION_MESSAGE = PASSWORD_VALIDATION_MESSAGE;

  ngOnInit() {
    // there should be an user at this stage
    if (!this.session.user()?.email) {
      this.router.navigate(['/auth/reset']);
    }
    this.form.get('password')?.valueChanges.subscribe(() => {
      this.errorMsg.set(null);
    });
    this.input()?.nativeElement.focus();
  }

  async doSubmit() {
    if (this.form.valid) {
      const { error } = await this.session.updatePassword(this.form.getRawValue().password);
      if (error) {
        this.errorMsg.set(error.message);
      } else {
        // this.ui.showSuccess('Password updated');
        this.session.signOut(this.router.createUrlTree(['/auth/login'], { queryParams: { withPass: 1 } }));
      }
    }
  }
}
