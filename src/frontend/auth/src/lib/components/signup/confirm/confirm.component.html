<h3>Activation email sent</h3>

<p>
  Congratulations, you’re almost done setting up your account!
  <br />
  We’ve sent an email with a verification code or a link to activate your account.
</p>

<form class="auth-wrapper" [formGroup]="form" (ngSubmit)="verifySubmit()">
  @if (errorMsg()) {
  <span class="caption status-danger">{{ errorMsg() }}</span>
  } @else if (successMsg()) {
  <span class="caption status-success">{{ successMsg() }}</span>
  }

  <input
    placeholder="Enter verification code"
    formControlName="token"
    #autofocus
    nbInput
    type="text"
    shape="semi-round"
    [status]="form.controls.token.dirty ? (!form.controls.token.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.token.touched && !form.controls.token.valid ? true : null"
  />

  <button type="submit" nbButton shape="round" size="medium" status="primary">Submit verification code</button>

  <ni-confirm-resend-button [resendOtp]="resendOtp.bind(this)" (countdownComplete)="onCountdownComplete()" />
</form>
