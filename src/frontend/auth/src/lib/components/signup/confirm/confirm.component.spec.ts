import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbFocusMonitor, NbStatusService } from '@nebular/theme';
import { provideNoopSessionStore } from '@neural/auth/testing';
import { MockProvider } from 'ng-mocks';
import { of } from 'rxjs';
import { SignupConfirmComponent } from './confirm.component';

describe('SignupConfirmComponent', () => {
  let component: SignupConfirmComponent;
  let fixture: ComponentFixture<SignupConfirmComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SignupConfirmComponent],
      providers: [MockProvider(NbFocusMonitor, { monitor: () => of(null) }), MockProvider(NbStatusService), provideNoopSessionStore()],
    }).compileComponents();

    fixture = TestBed.createComponent(SignupConfirmComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
