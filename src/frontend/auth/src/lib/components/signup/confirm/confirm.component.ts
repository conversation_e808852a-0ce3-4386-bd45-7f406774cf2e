import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  effect,
  inject,
  signal,
  viewChild,
  AfterViewInit,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
} from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { NbButtonModule, NbInputModule } from '@nebular/theme';
import { debounceTime, fromEvent, map, Subscription } from 'rxjs';
import { SessionStore } from '../../../store/session.store';
import { ResendButtonComponent } from './resend-button/resend-button.component';

@Component({
  selector: 'ni-auth-signup-confirm',
  standalone: true,
  imports: [ReactiveFormsModule, RouterModule, NbButtonModule, NbInputModule, ResendButtonComponent],
  templateUrl: './confirm.component.html',
  styleUrl: './confirm.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SignupConfirmComponent implements AfterViewInit, OnDestroy {
  @ViewChild(ResendButtonComponent) resendButton!: ResendButtonComponent;

  input = viewChild<ElementRef<HTMLInputElement>>('autofocus');
  private inputSubscription: Subscription | undefined;

  router = inject(Router);
  session = inject(SessionStore);

  errorMsg = signal<string | null>(null);
  successMsg = signal<string | null>(null);

  readonly form = inject(FormBuilder).nonNullable.group(
    {
      token: ['', [Validators.required]],
    },
    {
      updateOn: 'change',
    },
  );

  constructor() {
    effect(() => {
      this.input()?.nativeElement.focus();

      // there should be an user at this stage
      if (!this.session.user()?.email) {
        this.router.navigate(['/auth/verify']);
      }
    });
  }

  async verifySubmit() {
    if (this.form.valid) {
      const { error } = await this.session.verifyOtp({
        email: this.session.user()!.email,
        token: this.form.getRawValue().token,
        type: 'signup',
      });
      if (error) {
        this.errorMsg.set(error.message);
        this.successMsg.set('');
      } else {
        setTimeout(() => {
          window.location.href = '/';
        }, 1500);
      }
    }
  }

  async resendOtp() {
    if (this.session.user()?.email) {
      const { error } = await this.session.resendOtp({
        email: this.session.user()!.email!,
        type: 'signup',
      });
      if (error) {
        this.errorMsg.set(error.message);
        this.successMsg.set('');
      } else {
        this.errorMsg.set('');
        this.successMsg.set('Please check your email');
      }
    }
  }
  onCountdownComplete() {
    this.errorMsg.set('');
    this.successMsg.set('You can now resend the code');
  }

  ngAfterViewInit() {
    const inputElement = this.input();
    if (inputElement) {
      this.inputSubscription = fromEvent(inputElement.nativeElement, 'input')
        .pipe(
          debounceTime(300),
          map((event: Event) => {
            const input = event.target as HTMLInputElement;
            return input.value.trim();
          }),
        )
        .subscribe((value) => {
          this.form.patchValue({ token: value });
        });
    }
    if (this.resendButton) {
      this.resendButton.startCountdown(60);
    }
  }

  ngOnDestroy() {
    // Unsubscribe to avoid memory leaks
    if (this.inputSubscription) {
      this.inputSubscription.unsubscribe();
    }
  }
}
