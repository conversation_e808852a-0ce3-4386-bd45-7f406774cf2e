import { ChangeDetectorRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbButtonModule, NbInputModule, NbStatusService } from '@nebular/theme';
import { ResendButtonComponent } from './resend-button.component';

describe('ResendButtonComponent', () => {
  let component: ResendButtonComponent;
  let fixture: ComponentFixture<ResendButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NbButtonModule, NbInputModule, ResendButtonComponent],
      providers: [ChangeDetectorRef, NbStatusService],
    }).compileComponents();

    fixture = TestBed.createComponent(ResendButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
