import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, Output, signal } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NbButtonModule, NbInputModule } from '@nebular/theme';

@Component({
  selector: 'ni-confirm-resend-button',
  standalone: true,
  imports: [ReactiveFormsModule, NbButtonModule, NbInputModule],
  templateUrl: './resend-button.component.html',
  styleUrls: ['./resend-button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResendButtonComponent implements OnDestroy {
  @Input() resendOtp!: () => void;
  @Output() countdownComplete = new EventEmitter();
  countdown = signal(0);
  isDisabled = signal(false);
  private intervalId: ReturnType<typeof setInterval> | null = null;

  handleClick() {
    if (!this.isDisabled() && this.resendOtp) {
      this.resendOtp();
      this.startCountdown(60);
    }
  }

  startCountdown(seconds: number) {
    this.countdown.set(seconds);
    this.isDisabled.set(true);

    this.clearCountdown();

    this.intervalId = setInterval(() => {
      const currentCountdown = this.countdown();
      if (currentCountdown > 0) {
        this.countdown.set(currentCountdown - 1);
      } else {
        this.clearCountdown();
        this.isDisabled.set(false);
        this.countdownComplete.emit();
      }
    }, 1000);
  }

  private clearCountdown() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  ngOnDestroy() {
    this.clearCountdown();
  }
}
