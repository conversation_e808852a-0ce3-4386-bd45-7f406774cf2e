<form class="auth-wrapper" [formGroup]="form" (keyup.enter)="doSubmit()">
  <input
    formControlName="email"
    placeholder="Enter email address"
    #autofocus
    nbInput
    type="text"
    shape="semi-round"
    [status]="form.controls.email.dirty ? (!form.controls.email.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.email.touched && !form.controls.email.valid ? true : null"
  />
  @if (form.controls.email.touched && !form.controls.email.valid) { @if (form.controls.email.errors?.required) {
  <span class="caption status-danger"> Email is required </span>
  } @if (form.controls.email.errors?.email) {
  <span class="caption status-danger"> Email is invalid </span>
  } @if (form.controls.email.errors?.emailAsyncValidator) {
  <span class="caption status-danger">{{ form.controls.email.errors?.emailAsyncValidator }}</span>
  } }

  <input
    formControlName="password"
    placeholder="Enter password"
    nbInput
    type="password"
    shape="semi-round"
    [status]="form.controls.password.dirty ? (!form.controls.password.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.password.touched && !form.controls.password.valid ? true : null"
  />

  <input
    formControlName="passwordConfirm"
    placeholder="Confirm password"
    nbInput
    type="password"
    shape="semi-round"
    [status]="form.controls.passwordConfirm.dirty ? (!form.controls.passwordConfirm.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.passwordConfirm.touched && !form.controls.passwordConfirm.valid ? true : null"
  />
  @if ((form.controls.password.touched && !form.controls.password.valid) || (form.controls.passwordConfirm.touched && !form.valid)) { @if
  (!form.controls.password.valid) {
  <span class="caption status-danger"> {{ PASSWORD_VALIDATION_MESSAGE }} </span>
  } @if (form.controls.password.valid && form.controls.passwordConfirm.errors?.required) {
  <span class="caption status-danger"> Confirm password is required </span>
  } @if (form.errors?.passwordsMismatch) {
  <span class="caption status-danger"> Passwords must match </span>
  } }
</form>
