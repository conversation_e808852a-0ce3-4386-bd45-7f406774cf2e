import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbFocusMonitor, NbStatusService } from '@nebular/theme';
import { provideNoopAuthService } from '@neural/query/testing';
import { Mock<PERSON>rovider } from 'ng-mocks';
import { of } from 'rxjs';
import { SignupCredentialsComponent } from './credentials.component';

describe('SignupCredentialsComponent', () => {
  let component: SignupCredentialsComponent;
  let fixture: ComponentFixture<SignupCredentialsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SignupCredentialsComponent],
      providers: [Mock<PERSON>rovider(NbFocusMonitor, { monitor: () => of(null) }), MockProvider(NbStatusService), provideNoopAuthService()],
    }).compileComponents();

    fixture = TestBed.createComponent(SignupCredentialsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
