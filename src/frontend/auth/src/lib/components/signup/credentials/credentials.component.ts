import { ChangeDetectionStrategy, Component, DestroyRef, ElementRef, OnInit, inject, input, output, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { NbInputModule } from '@nebular/theme';
import { distinctUntilChanged, startWith } from 'rxjs/operators';
import { PASSWORD_VALIDATION_MESSAGE, passwordRegex } from '../../../constants';
import { emailAsyncValidator, passwordsMismatch } from '../../../validators';

@Component({
  selector: 'ni-auth-signup-credentials',
  standalone: true,
  imports: [ReactiveFormsModule, NbInputModule],
  templateUrl: './credentials.component.html',
  styleUrl: './credentials.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SignupCredentialsComponent implements OnInit {
  input = viewChild<ElementRef<HTMLInputElement>>('autofocus');

  canSubmit = output<boolean>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  signupData = input<any>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  signupDataChange = output<any>();
  submitEvent = output<boolean>();

  destroyRef = inject(DestroyRef);

  readonly form = inject(FormBuilder).nonNullable.group(
    {
      email: ['', [Validators.required, Validators.email], [emailAsyncValidator()]],
      password: ['', [Validators.required, Validators.pattern(passwordRegex)]],
      passwordConfirm: ['', [Validators.required]],
    },
    {
      updateOn: 'change',
      validators: [passwordsMismatch],
    },
  );

  readonly PASSWORD_VALIDATION_MESSAGE = PASSWORD_VALIDATION_MESSAGE;

  ngOnInit() {
    this.input()?.nativeElement.focus();

    if (this.signupData()?.password) {
      this.form.patchValue({
        email: this.signupData().email,
        password: this.signupData().password,
        passwordConfirm: this.signupData().password,
      });
    }

    this.form.statusChanges.pipe(takeUntilDestroyed(this.destroyRef), startWith(this.form.valid), distinctUntilChanged()).subscribe(() => {
      this.canSubmit.emit(this.form.valid);
    });

    this.form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.signupDataChange.emit({
        ...this.signupData(),
        email: this.form.getRawValue().email,
        password: this.form.getRawValue().password,
      });
    });
  }

  doSubmit() {
    if (this.form.valid) {
      this.submitEvent.emit(true);
    }
  }
}
