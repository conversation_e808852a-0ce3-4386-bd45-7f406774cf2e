<h2 class="title">Create Account</h2>

@if (step() === 1) {
<div class="auth-wrapper">
  <button nbButton class="status-neural" status="basic" shape="semi-round" (click)="session.signInWithOAuth('google')">
    <nb-icon icon="google" pack="oauth" />Continue with Google
  </button>
  <button nbButton class="status-neural" status="basic" shape="semi-round" (click)="session.signInWithOAuth('github')">
    <nb-icon icon="github" pack="oauth" />Continue with GitHub
  </button>
</div>

<div class="divider d-flex align-items-center m-3">
  <span>OR</span>
</div>
}

<div class="auth-wrapper">
  @switch (step()) { @case (1) {

  <ni-auth-signup-credentials [(signupData)]="signupData" (canSubmit)="canSubmit.set($event)" (submitEvent)="goNext()" />

  } @case (2) {

  <ni-auth-signup-user-data [(signupData)]="signupData" (canSubmit)="canSubmit.set($event)" (submitEvent)="goNext()" />

  } @case (3) {

  <ni-auth-signup-confirm />

  } } @if (step() < 3) {

  <button (click)="goNext()" [disabled]="!canSubmit()" type="button" nbButton status="primary" shape="round" size="medium">Next</button>

  <a (click)="goBack()" nbButton><nb-icon icon="arrow_back" /> Back</a>

  }
</div>
