import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { NbFocusMonitor, NbStatusService } from '@nebular/theme';
import { provideNoopSessionStore } from '@neural/auth/testing';
import { IconsModule } from '@neural/components';
import { provideNoopUIStore } from '@neural/components/testing';
import { provideNoopAuthService } from '@neural/query/testing';
import { MockProvider } from 'ng-mocks';
import { ReCaptchaV3Service } from 'ng-recaptcha-2';
import { of } from 'rxjs';
import { SignupComponent } from './signup.component';

describe('SignupComponent', () => {
  let component: SignupComponent;
  let fixture: ComponentFixture<SignupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SignupComponent, IconsModule],
      providers: [
        provideRouter([]),
        Mock<PERSON>rov<PERSON>(NbFocusMonitor, { monitor: () => of(null) }),
        MockProvider(NbStatusService),
        provideNoopSessionStore(),
        provideNoopAuthService(),
        provideNoopUIStore(),
        MockProvider(ReCaptchaV3Service),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(SignupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
