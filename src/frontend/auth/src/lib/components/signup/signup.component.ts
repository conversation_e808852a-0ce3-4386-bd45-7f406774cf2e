/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { Location } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, model, signal } from '@angular/core';
import { NbButtonModule, NbIconModule } from '@nebular/theme';
import { UIStore } from '@neural/components';
import { RecaptchaService } from '../../services';
import { SessionCredentials, SessionStore } from '../../store/session.store';
import { SignupConfirmComponent } from './confirm/confirm.component';
import { SignupCredentialsComponent } from './credentials/credentials.component';
import { SignupUserDataComponent } from './user-data/user-data.component';

@Component({
  selector: 'ni-auth-signup',
  standalone: true,
  imports: [NbButtonModule, NbIconModule, SignupCredentialsComponent, SignupUserDataComponent, SignupConfirmComponent],
  templateUrl: './signup.component.html',
  styleUrl: './signup.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'auth-component' },
  providers: [RecaptchaService],
})
export class SignupComponent {
  step = signal(1);
  signupData = model<SessionCredentials>();
  canSubmit = signal<boolean>(false);

  session = inject(SessionStore);
  ui = inject(UIStore);

  constructor(private location: Location, private recaptchaService: RecaptchaService) {}

  async goNext() {
    switch (this.step()) {
      case 1:
        this.canSubmit.set(false);
        this.step.set(2);
        break;
      case 2: {
        //TODO re-enable captcha
        const captchaValid = await this.recaptchaService.validateCaptcha();
        if (captchaValid) {
          this.session.signUp(this.signupData()!).then(({ error }) => {
            if (!error) {
              this.step.set(3);
              // Hide reCAPTCHA badge
              this.recaptchaService.hideRecaptchaBadge();
            }
          });
        } else {
          this.ui.showError('reCAPTCHA verification failed. Please try again.');
        }
        break;
      }
    }
  }

  goBack() {
    if (this.step() > 1) {
      this.step.set(this.step() - 1);
    } else {
      this.location.back();
    }
    return false;
  }
}
