<form class="auth-wrapper" [formGroup]="form" (keyup.enter)="doSubmit()">
  <input
    placeholder="First name"
    formControlName="first_name"
    #autofocus
    nbInput
    type="text"
    shape="semi-round"
    [status]="form.controls.first_name.dirty ? (!form.controls.first_name.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.first_name.touched && !form.controls.first_name.valid ? true : null"
  />
  @if (form.controls.first_name.touched && !form.controls.first_name.valid) { @if (form.controls.first_name.errors?.required) {
  <span class="caption status-danger"> First name is required </span>
  } @else if (form.controls.first_name.errors?.minlength) {
  <span class="caption status-danger"> First name must be at least 2 characters long. </span>
  } @else if (form.controls.first_name.errors?.maxlength) {
  <span class="caption status-danger"> First name cannot exceed 50 characters. </span>
  } @else if (form.controls.first_name.errors?.pattern) {
  <span class="caption status-danger"> First name can only contain letters, hyphens, and apostrophes. </span>
  } }

  <input
    placeholder="Last name"
    formControlName="last_name"
    nbInput
    type="text"
    shape="semi-round"
    [status]="form.controls.last_name.dirty ? (!form.controls.last_name.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.last_name.touched && !form.controls.last_name.valid ? true : null"
  />
  @if (form.controls.last_name.touched && !form.controls.last_name.valid) { @if (form.controls.last_name.errors?.maxlength) {
  <span class="caption status-danger"> Last name cannot exceed 50 characters. </span>
  } @else if (form.controls.last_name.errors?.pattern) {
  <span class="caption status-danger"> Last name can only contain letters, hyphens, and apostrophes. </span>
  } }

  <input
    placeholder="Username"
    formControlName="username"
    nbInput
    type="text"
    shape="semi-round"
    [status]="form.controls.username.dirty ? (!form.controls.username.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.username.touched && !form.controls.username.valid ? true : null"
  />
  @if (form.controls.username.touched && !form.controls.username.valid) { @if (form.controls.username.errors?.required) {
  <span class="caption status-danger"> Username is required </span>
  } @if (form.controls.username.errors?.pattern) {
  <span class="caption status-danger"> {{ USERNAME_VALIDATION_MESSAGE }} </span>
  } @if (form.controls.username.errors?.usernameAsyncValidator) {
  <span class="caption status-danger">{{ form.controls.username.errors?.usernameAsyncValidator }}</span>
  } }
</form>
