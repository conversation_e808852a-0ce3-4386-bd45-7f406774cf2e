import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbFocusMonitor, NbStatusService } from '@nebular/theme';
import { provideNoopAuthService } from '@neural/query/testing';
import { MockProvider } from 'ng-mocks';
import { of } from 'rxjs';
import { SignupUserDataComponent } from './user-data.component';

describe('SignupUserDataComponent', () => {
  let component: SignupUserDataComponent;
  let fixture: ComponentFixture<SignupUserDataComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SignupUserDataComponent],
      providers: [MockProvider(NbFocusMonitor, { monitor: () => of(null) }), MockProvider(NbStatusService), provideNoopAuthService()],
    }).compileComponents();

    fixture = TestBed.createComponent(SignupUserDataComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
