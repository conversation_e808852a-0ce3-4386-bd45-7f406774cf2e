import { ChangeDetectionStrategy, Component, DestroyRef, ElementRef, OnInit, inject, input, output, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { NbInputModule } from '@nebular/theme';
import { distinctUntilChanged, startWith } from 'rxjs/operators';
import { nameRegex, USERNAME_VALIDATION_MESSAGE, usernameRegex } from '../../../constants';
import { usernameAsyncValidator } from '../../../validators';

@Component({
  selector: 'ni-auth-signup-user-data',
  standalone: true,
  imports: [ReactiveFormsModule, NbInputModule],
  templateUrl: './user-data.component.html',
  styleUrl: './user-data.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SignupUserDataComponent implements OnInit {
  input = viewChild<ElementRef<HTMLInputElement>>('autofocus');

  canSubmit = output<boolean>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  signupData = input<any>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  signupDataChange = output<any>();
  submitEvent = output<boolean>();

  destroyRef = inject(DestroyRef);

  readonly USERNAME_VALIDATION_MESSAGE = USERNAME_VALIDATION_MESSAGE;

  readonly form = inject(FormBuilder).group(
    {
      first_name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50), Validators.pattern(nameRegex)]],
      last_name: ['', [Validators.maxLength(50), Validators.pattern(nameRegex)]],
      username: ['', [Validators.required, Validators.pattern(usernameRegex)], [usernameAsyncValidator()]],
    },
    {
      updateOn: 'change',
    },
  );

  ngOnInit() {
    this.input()?.nativeElement.focus();

    if (this.signupData()?.options?.data) {
      this.form.patchValue(this.signupData().options.data);
    }

    this.form.statusChanges.pipe(takeUntilDestroyed(this.destroyRef), startWith(this.form.valid), distinctUntilChanged()).subscribe(() => {
      this.canSubmit.emit(this.form.valid);
    });

    this.form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.signupDataChange.emit({
        ...this.signupData(),
        options: { data: this.form.getRawValue() },
      });
    });
  }

  doSubmit() {
    if (this.form.valid) {
      this.submitEvent.emit(true);
    }
  }
}
