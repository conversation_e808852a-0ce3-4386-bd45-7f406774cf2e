<h2>Activate Account</h2>

@if (url()) {

<div class="auth-wrapper">
  <p>Click the button to Activate your account and go to the Dashboard</p>

  <button (click)="goToUrl()" nbButton shape="round" size="medium" status="primary">Go to the Dashboard</button>
</div>

} @else {

<form class="auth-wrapper" [formGroup]="form" (ngSubmit)="resendSubmit()">
  @if (errorMsg()) {
  <span class="caption status-danger">{{ errorMsg() }}</span>
  } @else if (successMsg()) {
  <span class="caption status-success">{{ successMsg() }}</span>
  }

  <p>Get a new link in your email:</p>

  <input
    placeholder="Enter email address"
    formControlName="email"
    nbInput
    type="text"
    shape="semi-round"
    [status]="form.controls.email.dirty ? (!form.controls.email.valid ? 'danger' : 'basic') : 'basic'"
    [attr.aria-invalid]="form.controls.email.touched && !form.controls.email.valid ? true : null"
  />

  <button [disabled]="!form.valid" type="submit" nbButton shape="round" size="medium" status="basic">Resend link</button>
</form>

}
