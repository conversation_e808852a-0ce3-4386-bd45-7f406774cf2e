import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { NbFocusMonitor, NbStatusService } from '@nebular/theme';
import { provideNoopSessionStore } from '@neural/auth/testing';
import { Mock<PERSON>rovider } from 'ng-mocks';
import { of } from 'rxjs';
import { VerifyComponent } from './verify.component';

describe('VerifyComponent', () => {
  let component: VerifyComponent;
  let fixture: ComponentFixture<VerifyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [VerifyComponent],
      providers: [
        provideRouter([]),
        <PERSON>ck<PERSON>rov<PERSON>(NbFocusMonitor, { monitor: () => of(null) }),
        Mo<PERSON><PERSON><PERSON><PERSON>(NbStatusService),
        provideNoopSessionStore(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(VerifyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
