import { DOCUMENT } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, inject, model, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NbButtonModule, NbInputModule } from '@nebular/theme';
import { SessionStore } from '../../store/session.store';

@Component({
  selector: 'ni-auth-verify',
  standalone: true,
  imports: [ReactiveFormsModule, NbButtonModule, NbInputModule],
  templateUrl: './verify.component.html',
  styleUrl: './verify.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'auth-component' },
})
export class VerifyComponent implements OnInit {
  url = model<string | undefined>(); // coming as url queryParam

  document = inject(DOCUMENT);
  route = inject(ActivatedRoute);
  session = inject(SessionStore);

  errorMsg = signal<string | null>(null);
  successMsg = signal<string | null>(null);

  readonly form = inject(FormBuilder).nonNullable.group(
    {
      email: [this.session.user()?.email || '', [Validators.required, Validators.email]],
    },
    {
      updateOn: 'change',
    },
  );

  ngOnInit() {
    if (this.route.snapshot.fragment) {
      this.errorMsg.set(new URLSearchParams(this.route.snapshot.fragment).get('error_description'));
    }

    if (this.url()) {
      // minor security check to only allow supabase urls
      const { protocol, host } = new URL(this.url()!);
      if (protocol !== 'https:' || !host.endsWith('.supabase.co')) {
        this.url.set('');
      }
    }
  }

  goToUrl() {
    if (this.url()) {
      this.document.location.href = this.url()!;
    }
  }

  async resendSubmit() {
    if (this.form.valid) {
      const { error } = await this.session.signInWithOtp(this.form.getRawValue().email);
      if (error) {
        this.errorMsg.set(error.message);
        this.successMsg.set('');
      } else {
        this.errorMsg.set('');
        this.successMsg.set('Please check your email');
      }
    }
  }
}
