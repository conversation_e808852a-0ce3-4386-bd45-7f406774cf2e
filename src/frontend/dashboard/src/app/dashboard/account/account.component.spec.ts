import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbStatusService, NbWindowService } from '@nebular/theme';
import { provideNoopSessionStore, provideNoopUserStore } from '@neural/auth/testing';
import { MockComponent, MockProvider } from 'ng-mocks';
import { UserHeaderComponent } from '../shared';
import { AccountComponent } from './account.component';

describe('AccountComponent', () => {
  let component: AccountComponent;
  let fixture: ComponentFixture<AccountComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AccountComponent, MockComponent(UserHeaderComponent)],
      providers: [<PERSON>ckProvider(NbStatusService), MockProvider(NbWindowService), provideNoopSessionStore(), provideNoopUserStore()],
    }).compileComponents();

    fixture = TestBed.createComponent(AccountComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
