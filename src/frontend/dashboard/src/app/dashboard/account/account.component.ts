import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { NbButtonModule, NbWindowService } from '@nebular/theme';
import { SessionStore, UserStore } from '@neural/auth';
import { UserHeaderComponent } from '../shared';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { UserDetailComponent } from './profile/profile.component';

@Component({
  selector: 'ni-dashboard-account',
  standalone: true,
  imports: [NbButtonModule, UserHeaderComponent],
  templateUrl: './account.component.html',
  styleUrl: './account.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-section' },
})
export class AccountComponent {
  user = inject(UserStore);
  session = inject(SessionStore);

  protected windowService = inject(NbWindowService);

  editProfile() {
    this.windowService.open(UserDetailComponent, {
      title: 'User Details',
      closeOnBackdropClick: true,
      windowClass: 'ni-window-full-screen',
    });
  }

  changePassword() {
    this.windowService.open(ChangePasswordComponent, {
      title: 'Change Password',
      closeOnBackdropClick: true,
      windowClass: 'ni-window-full-screen',
    });
  }
}
