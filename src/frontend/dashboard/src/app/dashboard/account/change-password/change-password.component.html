@if (!reAuthenticated()) {
<p>
  A confirmation token will be sent to your email address. This token serves to verify your identity and enable the password update process.
</p>
} @else {
<div class="ni-change-password-grid" [formGroup]="form">
  <div class="g-col-12 form-group">
    <label class="form-label">Confirmation token</label>
    <input
      class="form-control status-neural"
      placeholder="Token sent to your email"
      formControlName="nonce"
      type="text"
      nbInput
      fullWidth
    />
    @if (form.controls.nonce.touched && !form.controls.nonce.valid) {
    <span class="caption status-danger"> Confirmation token is required </span>
    }
  </div>
  <div class="g-col-12 g-col-sm-6 form-group">
    <label class="form-label">New Password</label>
    <input class="form-control status-neural" placeholder="Enter New Password" formControlName="password" type="password" nbInput />
  </div>
  <div class="g-col-12 g-col-sm-6 form-group">
    <label class="form-label">Confirm password</label>
    <input class="form-control status-neural" placeholder="Confirm password" formControlName="passwordConfirm" type="password" nbInput />
  </div>
  @if ((form.controls.password.touched && !form.controls.password.valid) || (form.controls.passwordConfirm.touched && !form.valid)) { @if
  (!form.controls.password.valid) {
  <span class="g-col-12 caption status-danger">
    {{ PASSWORD_VALIDATION_MESSAGE }}
  </span>
  } @else if (form.controls.password.valid && form.controls.passwordConfirm.errors?.required) {
  <span class="g-col-12 caption status-danger"> Confirm password is required </span>
  } @else if (form.errors?.passwordsMismatch) {
  <span class="g-col-12 caption status-danger"> Passwords must match </span>
  } }
</div>
}
<div class="ni-actions ni-actions-responsive d-flex justify-content-center">
  <button (click)="doClose()" type="button" nbButton status="basic" shape="round" size="medium">Cancel</button>
  @if (!reAuthenticated()) {
  <button (click)="doSendToken()" type="button" nbButton status="primary" shape="round" size="medium">Send token</button>
  } @else {
  <button (click)="doSubmit()" [disabled]="!form.valid" type="button" nbButton status="primary" shape="round" size="medium">
    Change password
  </button>
  }
</div>
