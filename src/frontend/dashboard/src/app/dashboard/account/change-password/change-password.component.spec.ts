import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbFocusMonitor, NbStatusService, NbWindowRef } from '@nebular/theme';
import { provideNoopSessionStore } from '@neural/auth/testing';
import { provideNoopUIStore } from '@neural/components/testing';
import { MockProvider } from 'ng-mocks';
import { of } from 'rxjs';
import { ChangePasswordComponent } from './change-password.component';

describe('ChangePasswordComponent', () => {
  let component: ChangePasswordComponent;
  let fixture: ComponentFixture<ChangePasswordComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ChangePasswordComponent],
      providers: [
        MockProvider(NbFocusMonitor, { monitor: () => of(null) }),
        <PERSON>ck<PERSON><PERSON><PERSON>(NbWindowRef),
        <PERSON>ckProvider(NbStatusService),
        provideNoopSessionStore(),
        provideNoopUIStore(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ChangePasswordComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
