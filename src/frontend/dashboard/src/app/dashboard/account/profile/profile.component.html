<form class="ni-profile-grid" [formGroup]="form" (keyup.enter)="doSubmit()">
  <div class="g-col-sm-6 g-col-12 form-group">
    <label class="form-label">First Name</label>
    <input
      class="form-control status-neural"
      placeholder="Enter First Name"
      formControlName="first_name"
      type="text"
      nbInput
      [status]="form.controls.first_name.touched ? (!form.controls.first_name.valid ? 'danger' : 'basic') : 'basic'"
      [attr.aria-invalid]="form.controls.first_name.touched && !form.controls.first_name.valid ? true : null"
    />
    @if (form.controls.first_name.touched && !form.controls.first_name.valid) { @if (form.controls.first_name.errors?.required) {
    <span class="caption status-danger"> First name is required </span>
    } @else if (form.controls.first_name.errors?.minlength) {
    <span class="caption status-danger"> First name must be at least 2 characters long. </span>
    } @else if (form.controls.first_name.errors?.maxlength) {
    <span class="caption status-danger"> First name cannot exceed 50 characters. </span>
    } @else if (form.controls.first_name.errors?.pattern) {
    <span class="caption status-danger"> First name can only contain letters, hyphens, and apostrophes. </span>
    } }
  </div>

  <div class="g-col-sm-6 g-col-12 form-group">
    <label class="form-label">Last Name</label>
    <input
      class="form-control status-neural"
      placeholder="Enter Last Name"
      formControlName="last_name"
      type="text"
      nbInput
      [status]="form.controls.last_name.touched ? (!form.controls.last_name.valid ? 'danger' : 'basic') : 'basic'"
      [attr.aria-invalid]="form.controls.last_name.touched && !form.controls.last_name.valid ? true : null"
    />
    @if (form.controls.last_name.touched && !form.controls.last_name.valid) { @if (form.controls.last_name.errors?.maxlength) {
    <span class="caption status-danger"> Last name cannot exceed 50 characters. </span>
    } @else if (form.controls.last_name.errors?.pattern) {
    <span class="caption status-danger"> Last name can only contain letters, hyphens, and apostrophes. </span>
    } }
  </div>
  <div class="g-col-sm-6 g-col-12 form-group">
    <label class="form-label">Username</label>
    <input
      class="form-control status-neural"
      placeholder="Enter Username"
      formControlName="username"
      type="text"
      nbInput
      [status]="form.controls.username.touched ? (!form.controls.username.valid ? 'danger' : 'basic') : 'basic'"
      [attr.aria-invalid]="form.controls.username.touched && !form.controls.username.valid ? true : null"
    />
    @if (form.controls.username.touched && !form.controls.username.valid) { @if (form.controls.username.errors?.required) {
    <span class="caption status-danger"> Username is required </span>
    } @else if (form.controls.username.errors?.pattern) {
    <span class="caption status-danger"> {{ USERNAME_VALIDATION_MESSAGE }} </span>
    } @else if (form.controls.username.errors?.usernameAsyncValidator) {
    <span class="caption status-danger">{{ form.controls.username.errors?.usernameAsyncValidator }}</span>
    } }
  </div>

  <div class="g-col-12 form-group">
    <div class="d-flex justify-content-center row">
      <div style="width: 270px" class="align-items-center upload-button d-flex gap-3 justify-content-center p-3 rounded-pill">
        <nb-user size="large" [onlyPicture]="true" [picture]="previewAvatar() || user.user()?.avatar_url! || 'assets/account-circle.svg'">
        </nb-user>
        <!-- Button that triggers the file input -->
        <button nbButton fullWidth shape="round" class="rounded-pill fw-bold" status="ni-tertiary" size="small" (click)="fileInput.click()">
          CHOOSE PICTURE
        </button>
        <!-- Hidden input element -->
        <input #fileInput type="file" (change)="onFileChange($event)" [accept]="acceptFileTypes" style="display: none" />
      </div>
      @if(form.get('file')?.hasError('invalidType')){
      <div class="caption status-danger text-center mt-2">Invalid file type. Only {{ allowedFileTypes }} are allowed.</div>
      } @if(form.get('file')?.hasError('maxSize')){
      <div class="caption status-danger text-center mt-2">File size exceeds the {{ AVATAR_MAX_FILE_SIZE_MB }}MB limit.</div>
      }
    </div>
  </div>
</form>

<div class="ni-actions ni-actions-responsive d-flex justify-content-center">
  <button (click)="doClose()" type="button" nbButton status="basic" shape="round" size="medium">Discard</button>
  <button (click)="doSubmit()" [disabled]="!form.valid" type="button" nbButton status="primary" shape="round" size="medium">
    Save Changes
  </button>
</div>
