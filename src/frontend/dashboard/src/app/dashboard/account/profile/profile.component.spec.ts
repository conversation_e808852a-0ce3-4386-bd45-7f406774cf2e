import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbFocusMonitor, NbStatusService, NbWindowRef } from '@nebular/theme';
import { provideNoopSessionStore, provideNoopUserStore } from '@neural/auth/testing';
import { provideNoopUIStore } from '@neural/components/testing';
import { provideNoopAuthService } from '@neural/query/testing';
import { MockProvider } from 'ng-mocks';
import { of } from 'rxjs';
import { UserDetailComponent } from './profile.component';

describe('UserDetailComponent', () => {
  let component: UserDetailComponent;
  let fixture: ComponentFixture<UserDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UserDetailComponent],
      providers: [
        MockProvider(NbFocusMonitor, { monitor: () => of(null) }),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(NbWindowRef),
        <PERSON><PERSON><PERSON>rov<PERSON>(NbStatusService),
        provideNoopSessionStore(),
        provideNoopUserStore(),
        provideNoopUIStore(),
        provideNoopAuthService(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(UserDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
