import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, ReactiveFormsModule, Validators } from '@angular/forms';
import { NbButtonModule, NbInputModule, NbSpinnerModule, NbUserModule, NbWindowRef } from '@nebular/theme';
import { SessionStore, USERNAME_VALIDATION_MESSAGE, UserStore, nameRegex, usernameAsyncValidator, usernameRegex } from '@neural/auth';
import { UIStore } from '@neural/components';
import { AVATAR_MAX_FILE_SIZE, AVATAR_VALID_FILE_TYPES } from '@neural/models';
import { take, tap } from 'rxjs';

@Component({
  selector: 'ni-user-detail',
  standalone: true,
  imports: [ReactiveFormsModule, NbButtonModule, NbInputModule, NbSpinnerModule, NbUserModule],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-popup' },
})
export class UserDetailComponent {
  user = inject(UserStore);
  session = inject(SessionStore);
  ui = inject(UIStore);

  previewAvatar = signal<string | null>(null);

  private windowRef = inject(NbWindowRef);

  readonly AVATAR_MAX_FILE_SIZE_MB = AVATAR_MAX_FILE_SIZE / (1024 * 1024); // Convert bytes to MB

  readonly allowedFileTypes = AVATAR_VALID_FILE_TYPES.map((type) => type.split('/')[1].toUpperCase()).join(', ');

  readonly acceptFileTypes = AVATAR_VALID_FILE_TYPES.join(','); // Join types for the 'accept' attribute

  readonly USERNAME_VALIDATION_MESSAGE = USERNAME_VALIDATION_MESSAGE;

  readonly form = inject(FormBuilder).group(
    {
      first_name: [
        this.user.user()?.first_name || '',
        [Validators.required, Validators.minLength(2), Validators.maxLength(50), Validators.pattern(nameRegex)],
      ],

      last_name: [this.user.user()?.last_name, [Validators.maxLength(50), Validators.pattern(nameRegex)]],
      username: [
        this.user.user()?.username || '',
        [Validators.required, Validators.pattern(usernameRegex)],
        [usernameAsyncValidator(this.user.user()?.username)],
      ],
      file: [null as File | null], // Allow null initially until a file is selected
      avatar_url: [this.user.user()?.avatar_url || ''], // Hidden field, not shown in the UI
    },
    {
      updateOn: 'change',
    },
  );

  onFileChange(event: Event) {
    const file = (event.target as HTMLInputElement)?.files?.[0];
    if (!file) {
      this.clearFileValidators(); // Clear validators if no file is selected
      return;
    }

    // Set dynamic file validators
    this.setFileValidators();
    // Validate file type
    if (!AVATAR_VALID_FILE_TYPES.includes(file.type)) {
      this.setFileError('invalidType');
      return;
    }

    // Validate file size
    if (file.size > AVATAR_MAX_FILE_SIZE) {
      this.setFileError('maxSize');
      return;
    }

    // Set the file if it passes all validations
    this.form.get('file')?.setValue(file);

    // Preview the image
    this.previewImage(file);
  }

  previewImage(file: File) {
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
      const result = e.target?.result as string | null; // The result can be either a string or null
      if (result) {
        this.previewAvatar.set(result); // Set the image data URL if available
      } else {
        this.previewAvatar.set(null); // Set to null if no result
        console.error('Failed to load image preview');
      }
    };

    reader.onerror = (error) => {
      console.error('Error reading file:', error); // Handle read errors
      this.previewAvatar.set(null); // Reset the avatar preview on error
    };

    reader.readAsDataURL(file); // Read the file as Data URL
  }

  setFileValidators() {
    const fileControl = this.form.get('file');
    if (fileControl) {
      fileControl.setValidators([Validators.required]);
      fileControl.updateValueAndValidity();
    }
  }

  clearFileValidators() {
    const fileControl = this.form.get('file');
    if (fileControl) {
      fileControl.clearValidators();
      fileControl.updateValueAndValidity();
    }
  }

  setFileError(errorType: string) {
    const fileControl = this.form.get('file');
    if (fileControl) {
      fileControl.setErrors({ [errorType]: true });
    }
  }

  doClose() {
    this.windowRef.close();
  }

  async doSubmit() {
    if (this.form.valid) {
      this.ui.setLoading();
      const file = this.form.get('file')?.value as File | null; // Explicitly type as File or null
      if (file) {
        this.user
          .uploadAvatar(file)
          .pipe(
            tap({
              next: () => {
                this.updateMetaData();
              },
            }),
            take(1),
          )
          .subscribe();
      } else {
        this.updateMetaData();
      }
    }
  }

  async updateMetaData() {
    const data = this.form.getRawValue();
    const { file, avatar_url, ...metadata } = data;
    const { first_name, last_name, username } = metadata;
    if (!first_name || !username) {
      return;
    }
    const { error } = await this.session.updatedMetadata({
      data: {
        first_name,
        last_name,
        username,
      },
    });
    if (!error) {
      this.ui.showSuccess('Profile updated successfully');
      this.doClose();
    }
  }
}
