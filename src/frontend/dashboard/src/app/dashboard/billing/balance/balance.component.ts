import { CurrencyPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { UserStore } from '@neural/auth';

@Component({
  selector: 'ni-dashboard-billing-balance',
  standalone: true,
  imports: [CurrencyPipe],
  templateUrl: './balance.component.html',
  styleUrl: './balance.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-card ni-card-row' },
})
export class BalanceComponent {
  user = inject(UserStore);
}
