import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { NbFocusMonitor, NbStatusService, NbWindowService } from '@nebular/theme';
import { provideNoopUserStore } from '@neural/auth/testing';
import { IconsModule } from '@neural/components';
import { provideNoopUIStore } from '@neural/components/testing';
import { provideNoopCardsStore, provideNoopPaymentsStore } from '@neural/data-access/testing';
import { MockComponent, MockProvider } from 'ng-mocks';
import { provideNgxStripe } from 'ngx-stripe';
import { of } from 'rxjs';
import { AutoTopupComponent } from '../settings/auto-topup/auto-topup.component';
import { BalanceComponent } from './balance/balance.component';
import { BillingComponent } from './billing.component';
import { PaymentsComponent } from './payments/payments.component';
import { TopUpComponent } from './top-up/top-up.component';

describe('BillingComponent', () => {
  let component: BillingComponent;
  let fixture: ComponentFixture<BillingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        BillingComponent,
        IconsModule,
        MockComponent(PaymentsComponent),
        MockComponent(BalanceComponent),
        MockComponent(TopUpComponent),
        MockComponent(AutoTopupComponent),
      ],
      providers: [
        provideRouter([]),
        MockProvider(NbFocusMonitor, { monitor: () => of(null) }),
        MockProvider(NbStatusService),
        MockProvider(NbWindowService),
        provideNgxStripe(process.env.STRIPE_API_KEY, {
          locale: 'en',
        }),
        provideNoopUserStore(),
        provideNoopUIStore(),
        provideNoopCardsStore(),
        provideNoopPaymentsStore(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(BillingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
