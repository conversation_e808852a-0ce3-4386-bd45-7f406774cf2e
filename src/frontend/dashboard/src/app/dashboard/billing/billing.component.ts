import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { UIStore } from '@neural/components';
import { CardsStore, PaymentsStore } from '@neural/data-access';
import { getStripeErrorMessage } from '@neural/models';
import { injectStripe } from 'ngx-stripe';
import { catchError, switchMap } from 'rxjs';
import { AutoTopupComponent } from '../settings/auto-topup/auto-topup.component';
import { BalanceComponent } from './balance/balance.component';
import { PaymentsComponent } from './payments/payments.component';
import { TopUpComponent } from './top-up/top-up.component';

@Component({
  selector: 'ni-dashboard-billing',
  standalone: true,
  imports: [PaymentsComponent, BalanceComponent, TopUpComponent, AutoTopupComponent],
  templateUrl: './billing.component.html',
  styleUrl: './billing.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-section' },
})
export class BillingComponent {
  route = inject(ActivatedRoute);
  ui = inject(UIStore);
  router = inject(Router);
  cardsStore = inject(CardsStore);
  payments = inject(PaymentsStore);
  stripe = injectStripe();

  constructor() {
    this.route.queryParams.pipe(takeUntilDestroyed()).subscribe((params) => {
      if (params.checkout) {
        this.ui.showSuccess('Top up initiated', 'Your balance will be updated soon', 30000);
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: {},
          replaceUrl: true, // Replace the current URL in the history
        });
      }
      if (params.handle_next_action && params.token && params.id) {
        this.payments
          .getStatusAndClientSecretDto({
            id: Number(params.id),
            secretToken: params.token,
          })
          .pipe(
            switchMap((payment) => {
              if (payment.status === 'requires_action' && payment.client_secret) {
                return this.stripe
                  .handleNextAction({
                    clientSecret: payment.client_secret,
                  })
                  .pipe(
                    switchMap(({ paymentIntent, error }) => {
                      if (error) {
                        this.ui.showError(getStripeErrorMessage(error.code, error.message));
                      } else if (paymentIntent?.status === 'succeeded') {
                        this.ui.showSuccess('Auto top-up payment confirmed. Your balance will be updated soon.');
                      } else if (paymentIntent?.status === 'requires_payment_method') {
                        const errorCode = paymentIntent?.last_payment_error?.code;
                        let errorMessage = 'Auto top-up payment failed. Please update your auto top-up settings.';
                        if (errorCode === 'payment_intent_authentication_failure') {
                          errorMessage = 'Auto top-up payment confirmation failed. Please update your auto top-up settings.';
                        }
                        this.ui.showError(errorMessage);
                      } else if (paymentIntent?.status === 'canceled') {
                        const cancellationMessage =
                          paymentIntent.cancellation_reason === 'abandoned'
                            ? 'Your auto top-up payment was canceled because it wasn’t confirmed in time. Please adjust your auto top-up settings.'
                            : 'Your auto top-up payment was canceled.';
                        this.ui.showError(cancellationMessage);
                      }
                      return [];
                    }),
                  );
              } else {
                if (payment.status === 'succeeded') {
                  this.ui.showSuccess(
                    payment.isConfirmed ? 'This payment was already confirmed' : 'This payment was confirmed automatically.',
                  );
                } else if (payment.status === 'requires_payment_method') {
                  const errorCode = payment?.lastPaymentError?.code;
                  let errorMessage = 'Auto top-up payment failed. Please update your auto top-up settings.';
                  if (errorCode === 'payment_intent_authentication_failure') {
                    errorMessage = 'Auto top-up payment confirmation failed. Please update your auto top-up settings.';
                  }
                  this.ui.showError(errorMessage);
                } else if (payment.status === 'canceled') {
                  const cancellationMessage =
                    payment.cancellation_reason === 'abandoned'
                      ? 'Your auto top-up payment was canceled because it wasn’t confirmed in time. Please adjust your auto top-up settings.'
                      : 'Your auto top-up payment was canceled.';
                  this.ui.showError(cancellationMessage);
                }
                return [];
              }
            }),
            catchError((error) => {
              this.ui.showError(error.error?.message || 'An unknown error occurred while processing your payment');
              return [];
            }),
          )
          .subscribe({
            complete: () => {
              this.router.navigate([], {
                relativeTo: this.route,
                queryParams: {},
                replaceUrl: true,
              });
            },
          });
      }
    });
  }
}
