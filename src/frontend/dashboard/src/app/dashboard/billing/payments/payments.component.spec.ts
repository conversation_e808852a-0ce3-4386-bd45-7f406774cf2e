import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideNoopPaymentsStore } from '@neural/data-access/testing';
import { MockComponent } from 'ng-mocks';
import { PaymentsListComponent } from '../../shared';
import { PaymentsComponent } from './payments.component';

describe('PaymentsComponent', () => {
  let component: PaymentsComponent;
  let fixture: ComponentFixture<PaymentsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PaymentsComponent, MockComponent(PaymentsListComponent)],
      providers: [provideNoopPaymentsStore()],
    }).compileComponents();

    fixture = TestBed.createComponent(PaymentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
