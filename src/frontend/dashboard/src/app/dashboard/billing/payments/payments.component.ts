import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { PaymentsStore } from '@neural/data-access';
import { PaymentsListComponent } from '../../shared';

@Component({
  selector: 'ni-dashboard-billing-payments',
  standalone: true,
  imports: [PaymentsListComponent],
  templateUrl: './payments.component.html',
  styleUrl: './payments.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-card' },
})
export class PaymentsComponent {
  paymentsStore = inject(PaymentsStore);
}
