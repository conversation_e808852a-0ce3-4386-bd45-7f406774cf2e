<form
  class="grid gap-3"
  [nbSpinner]="isLoading()"
  [formGroup]="form"
  ngxStripeElements
  [stripe]="stripe"
  [elementsOptions]="elementsOptions"
  (elements)="elements.set($event)"
  (keyup.enter)="doSubmit()"
>
  <div class="g-col-12 form-group">
    <label class="form-label">Card Number</label>
    <ngx-stripe-card class="form-control status-neural" (change)="onCardChange($event)" [options]="cardOptions" />
  </div>

  <div class="g-col-12 g-col-md-6 form-group">
    <ngx-stripe-address (ready)="onReady()" (change)="onAddressChange($event)" [options]="shippingAddressOptions" />
  </div>

  <div class="g-col-12 g-col-md-6 input-full-width">
    <div class="ni-add-card-grid">
      <div class="g-col-12 form-group">
        <label class="form-label">Company Name <small>(optional)</small></label>
        <input
          class="form-control status-neural input-full-width"
          placeholder="Enter text"
          formControlName="company_name"
          type="text"
          nbInput
          [status]="form.controls.company_name.touched ? (!form.controls.company_name.valid ? 'danger' : 'basic') : 'basic'"
          [attr.aria-invalid]="form.controls.company_name.touched && !form.controls.company_name.valid ? true : null"
        />
      </div>

      <div class="g-col-12 form-group">
        <label class="form-label">Company ID <small>(optional)</small></label>
        <input
          class="form-control status-neural input-full-width"
          formControlName="company_id"
          type="text"
          nbInput
          placeholder="Enter text"
        />
      </div>

      <div class="g-col-12 form-group">
        <label class="form-label">Tax ID <small>(optional)</small></label>
        <input class="form-control status-neural input-full-width" formControlName="tax_id" type="text" nbInput placeholder="Enter ID" />
      </div>
    </div>
  </div>
</form>
<form [class.d-none]="isLoading()" [formGroup]="autoTopUpform">
  @if(userStore.user()?.auto_topup_active){
  <div class="row g-3 mt-3">
    <div class="col-12 col-sm-6 form-group offset-sm-3 text-left text-sm-center">
      <nb-checkbox formControlName="use_this_card">Use this card for top up</nb-checkbox>
    </div>
  </div>
  } @else{
  <div class="row g-3 mt-3">
    <div class="col-12 col-sm-6 form-group offset-sm-3 text-left text-sm-center">
      <nb-checkbox formControlName="auto_topup_active">Enable auto top-up</nb-checkbox>
    </div>
  </div>
  @if(autoTopUpform.get('auto_topup_active')?.value){

  <div class="mt-3">
    <p class="text-warning">You may need to confirm auto top-up by checking your email for a message from Neural Internet.</p>
  </div>

  <div>
    <p class="privacy-info">
      By enabling auto top-up, you allow us to charge your card as needed, even without your direct confirmation. See our
      <a routerLink="/legal/privacy-policy">privacy policy</a> for a full explanation of these terms.
    </p>
  </div>

  <div class="row g-3 mt-3">
    <div class="col-sm-6 form-group">
      <nb-form-field>
        <label class="form-label">Minimum threshold amount</label>
        <input
          placeholder="Enter amount of USD here"
          formControlName="auto_topup_threshold"
          class="status-neural"
          type="text"
          mask="separator.2"
          thousandSeparator=","
          decimalMarker="."
          [allowNegativeNumbers]="false"
          nbInput
          [status]="
            !autoTopUpform.valid && autoTopUpform.controls.auto_topup_threshold.touched
              ? !autoTopUpform.controls.auto_topup_threshold.value
                ? 'danger'
                : 'basic'
              : 'basic'
          "
          [attr.aria-invalid]="
            !autoTopUpform.valid &&
            autoTopUpform.controls.auto_topup_threshold.touched &&
            !autoTopUpform.controls.auto_topup_threshold.value
              ? true
              : null
          "
        />
        <nb-icon nbSuffix icon="attach_money" />
      </nb-form-field>
    </div>
    <div class="col-sm-6 form-group">
      <nb-form-field>
        <label class="form-label">Auto top-up amount</label>
        <input
          placeholder="Enter amount of USD here"
          formControlName="auto_topup_amount"
          class="status-neural"
          type="text"
          mask="separator.2"
          thousandSeparator=","
          decimalMarker="."
          [allowNegativeNumbers]="false"
          nbInput
          [status]="
            !autoTopUpform.valid && autoTopUpform.controls.auto_topup_amount.touched
              ? !autoTopUpform.controls.auto_topup_amount.value
                ? 'danger'
                : 'basic'
              : 'basic'
          "
          [attr.aria-invalid]="
            !autoTopUpform.valid && autoTopUpform.controls.auto_topup_amount.touched && !autoTopUpform.controls.auto_topup_amount.value
              ? true
              : null
          "
        />
        <nb-icon nbSuffix icon="attach_money" />
      </nb-form-field>
      @if (autoTopUpform.controls.auto_topup_amount.errors?.min) {
      <span class="caption status-danger"> Minimum auto top-up amount is ${{ MIN_AUTO_TOPUP_AMOUNT_IN_CENTS | centsToDollars }}. </span>
      }
    </div>
  </div>
  } }
</form>
<div class="ni-actions ni-actions-responsive">
  <button (click)="doClose()" type="button" nbButton status="basic" shape="round" size="medium">Discard</button>
  <button
    (click)="doSubmit()"
    [disabled]="isSubmitting() || !form.valid || !autoTopUpform.valid || !isCardValid() || !isAddressValid()"
    [nbSpinner]="isSubmitting()"
    type="button"
    nbButton
    status="primary"
    shape="round"
    size="medium"
  >
    Save Card
  </button>
</div>
