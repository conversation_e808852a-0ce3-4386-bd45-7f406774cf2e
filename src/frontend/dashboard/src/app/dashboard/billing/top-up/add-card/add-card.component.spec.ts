import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbFocusMonitor, NbStatusService, NbWindowRef } from '@nebular/theme';
import { provideNoopSessionStore, provideNoopUserStore } from '@neural/auth/testing';
import { provideNoopUIStore } from '@neural/components/testing';
import { provideNoopCardsStore } from '@neural/data-access/testing';
import { MockProvider } from 'ng-mocks';
import { provideNgxStripe } from 'ngx-stripe';
import { of } from 'rxjs';
import { AddCardComponent } from './add-card.component';

describe('AddCardComponent', () => {
  let component: AddCardComponent;
  let fixture: ComponentFixture<AddCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddCardComponent],
      providers: [
        MockProvider(NbFocusMonitor, { monitor: () => of(null) }),
        <PERSON><PERSON><PERSON>rov<PERSON>(NbStatusService),
        Mock<PERSON>rov<PERSON>(NbWindowRef),
        provideNgxStripe(process.env.STRIPE_API_KEY, {
          locale: 'en',
        }),
        provideNoopCardsStore(),
        provideNoopSessionStore(),
        provideNoopUIStore(),
        provideNoopUserStore(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddCardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
