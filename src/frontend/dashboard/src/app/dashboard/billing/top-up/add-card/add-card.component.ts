import { <PERSON><PERSON><PERSON>cyPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, effect, inject, model, signal, viewChild } from '@angular/core';
import { AbstractControl, FormBuilder, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { NbButtonModule, NbCheckboxModule, NbFormFieldModule, NbInputModule, NbSpinnerModule, NbWindowRef } from '@nebular/theme';
import { SessionStore, UserStore } from '@neural/auth';
import { CentsToDollarsPipe, UIStore } from '@neural/components';
import { CardsStore } from '@neural/data-access';
import { MIN_AUTO_TOPUP_AMOUNT_IN_CENTS } from '@neural/models';
import { cardOptions, elementsOptions, shippingAddressOptions } from '@neural/stripe';
import { StripeAddressElementChangeEvent, StripeCardElementChangeEvent, StripeElements } from '@stripe/stripe-js';
import { NgxMaskDirective } from 'ngx-mask';
import { StripeAddressComponent, StripeCardComponent, StripeElementsDirective, injectStripe } from 'ngx-stripe';
import { catchError, from, map, switchMap, tap, throwError } from 'rxjs';

@Component({
  selector: 'ni-popup-add-card',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NbButtonModule,
    NbCheckboxModule,
    NbInputModule,
    NbSpinnerModule,
    StripeAddressComponent,
    StripeCardComponent,
    StripeElementsDirective,
    NbFormFieldModule,
    NgxMaskDirective,
    RouterLink,
    CentsToDollarsPipe,
  ],
  providers: [CurrencyPipe],
  templateUrl: './add-card.component.html',
  styleUrl: './add-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-popup' },
})
export class AddCardComponent {
  cards = inject(CardsStore);
  userStore = inject(UserStore);
  session = inject(SessionStore);
  ui = inject(UIStore);
  stripe = injectStripe();

  isSubmitting = signal(false);
  elements = model<StripeElements>();
  card = viewChild(StripeCardComponent);
  address = viewChild(StripeAddressComponent);

  private windowRef = inject(NbWindowRef);

  elementsOptions = elementsOptions;
  cardOptions = cardOptions;
  shippingAddressOptions = shippingAddressOptions;

  readonly MIN_AUTO_TOPUP_AMOUNT_IN_CENTS = MIN_AUTO_TOPUP_AMOUNT_IN_CENTS;
  readonly MIN_AUTO_TOPUP_AMOUNT_IN_DOLLARS = MIN_AUTO_TOPUP_AMOUNT_IN_CENTS / 100;

  readonly autoTopUpform = inject(FormBuilder).nonNullable.group(
    {
      auto_topup_active: [false],
      auto_topup_threshold: [null as number | null],
      auto_topup_amount: [null as number | null, [Validators.min(this.MIN_AUTO_TOPUP_AMOUNT_IN_DOLLARS)]],
      auto_topup_card_id: [null as string | null],
      use_this_card: [false],
    },
    {
      validators: [
        (form: AbstractControl): ValidationErrors | null => {
          if (form.value.auto_topup_active) {
            if (!form.value.auto_topup_threshold || !form.value.auto_topup_amount) {
              return { required: true };
            }
          }
          return null;
        },
      ],
    },
  );

  readonly form = inject(FormBuilder).group(
    {
      company_name: [''],
      company_id: [''],
      tax_id: [''],
    },
    {
      updateOn: 'change',
    },
  );

  isLoading = signal(true);
  isCardValid = signal(false);
  isAddressValid = signal(false);

  constructor() {
    effect(() => {
      const user = this.userStore.user();
      if (user) {
        this.autoTopUpform.reset(user, { emitEvent: false });
        setTimeout(() => {
          this.autoTopUpform.markAsPristine();
        }, 10);
      }
      //Set auto top card id to null if card list is empty
      const cards = this.cards.list();
      const auto_topup_card_id = this.autoTopUpform.controls.auto_topup_card_id.value;
      if (!cards.length || !cards.some((card) => card.id === auto_topup_card_id)) {
        this.autoTopUpform.controls.auto_topup_card_id.setValue(null);
      }
    });
  }

  onReady() {
    this.isLoading.set(false);
  }

  onCardChange(event: StripeCardElementChangeEvent) {
    this.isCardValid.set(event.complete);
  }

  onAddressChange(event: StripeAddressElementChangeEvent) {
    this.isAddressValid.set(event.complete);
  }

  doClose() {
    this.windowRef.close();
  }

  doSubmit() {
    if (this.form.valid && this.autoTopUpform.valid && this.isCardValid() && this.isAddressValid() && !this.isSubmitting()) {
      this.isSubmitting.set(true);
      from(this.address()!.getValue())
        .pipe(
          switchMap(({ value: { name, address } }) => {
            return this.stripe.createPaymentMethod({
              elements: this.elements()!,
              params: {
                billing_details: {
                  name,
                  email: this.session.user()?.email, // FIXME connection with the current user email?
                  address: {
                    ...address,
                    line2: address.line2 || undefined,
                  },
                },
                metadata: {
                  ...this.form.value,
                },
              },
            });
          }),
          switchMap(({ paymentMethod, error }) => {
            // Handle any potential errors from Stripe
            if (error) {
              throw new Error(error.message || 'Failed to create payment method');
            }

            return this.cards.setupIntent(paymentMethod.id).pipe(
              map((response) => ({
                response,
              })),
              catchError((error) => {
                return throwError(() => error);
              }),
            );
          }),
          switchMap(({ response }) => {
            const clientSecret = response.client_secret;
            if (!clientSecret) {
              throw new Error('Failed to confirm card setup. Please try again.');
            }

            return this.stripe
              .confirmCardSetup(clientSecret, {
                payment_method: response.card.id,
              })
              .pipe(
                map((intent) => ({
                  intent,
                  response,
                })),
                catchError((error) => {
                  return throwError(() => new Error('Failed to confirm card setup. Please try again.'));
                }),
              );
          }),
          catchError((error) => {
            return throwError(() => error);
          }),
        )
        .subscribe({
          next: ({ intent: { setupIntent, error }, response }) => {
            if (error) {
              this.ui.showError(error.message || 'Failed to confirm card setup. Please try again.');
              this.isSubmitting.set(false);
              return;
            }
            switch (setupIntent.status) {
              case 'succeeded':
                this.ui.showSuccess('Your card has been added successfully!');
                this.cards.addCardToList(response.card);
                try {
                  // Get the current state of auto top-up from user store
                  const userAutoTopUpActive = this.userStore.user()?.auto_topup_active;
                  const isAutoTopUpEnabled = this.getValue().auto_topup_active;
                  const useThisCard = this.autoTopUpform.get('use_this_card')?.value;

                  // Check conditions and update auto top-up form or close it
                  if (isAutoTopUpEnabled && !userAutoTopUpActive) {
                    // Enabling auto top-up
                    this.autoTopUpform.patchValue({ auto_topup_card_id: response.card.id });
                    this.setupTopUp();
                  } else if (useThisCard) {
                    // Update auto top-up to new card
                    this.autoTopUpform.patchValue({ auto_topup_card_id: response.card.id });
                    this.setupTopUp();
                  } else {
                    // Close the form
                    this.doClose();
                  }
                } catch (e) {
                  // Log the error and inform the user
                  this.ui.showSuccess('Your card has been added successfully, but auto top-up update failed.');
                } finally {
                  // this.isSubmitting.set(false); // Ensure the submission state is reset
                }
                break;
              case 'processing':
                this.ui.showSuccess('Your card is being processed, We will update you when processing is complete.');
                this.isSubmitting.set(false);
                break;
              case 'requires_payment_method':
                this.ui.showSuccess('Failed to process payment details. Please try another payment method.');
                this.isSubmitting.set(false);
                break;
            }
          },
          error: (error) => {
            this.ui.showError(error.error?.message || 'Failed to add card. Please try again.');
            this.isSubmitting.set(false);
          },
        });
    }
  }

  setupTopUp() {
    if (!this.autoTopUpform.valid) {
      this.autoTopUpform.markAllAsTouched();
      return;
    }
    this.userStore
      .updateProfile(this.getValue())
      .pipe(
        tap(() => {
          this.ui.showSuccess('Auto top-up has been successfully updated!');
          this.autoTopUpform.markAsPristine();
          this.isSubmitting.set(false);
          this.doClose();
        }),
      )
      .subscribe();
  }

  private getValue() {
    return {
      auto_topup_active: !!this.autoTopUpform.get('auto_topup_active')!.value,
      auto_topup_threshold: this.autoTopUpform.get('auto_topup_threshold')!.value || null,
      auto_topup_amount: this.autoTopUpform.get('auto_topup_amount')!.value || null,
      auto_topup_card_id: this.autoTopUpform.get('auto_topup_card_id')!.value || null,
    };
  }
}
