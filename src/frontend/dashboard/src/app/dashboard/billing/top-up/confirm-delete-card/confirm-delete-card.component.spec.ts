import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbStatusService, NbWindowRef } from '@nebular/theme';
import { IconsModule } from '@neural/components';
import { MockProvider } from 'ng-mocks';
import { ConfirmDeleteCardComponent } from './confirm-delete-card.component';

describe('ConfirmDeleteCardComponent', () => {
  let component: ConfirmDeleteCardComponent;
  let fixture: ComponentFixture<ConfirmDeleteCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ConfirmDeleteCardComponent, IconsModule],
      providers: [<PERSON><PERSON><PERSON><PERSON>ider(NbStatusService), MockProvider(NbWindowRef)],
    }).compileComponents();

    fixture = TestBed.createComponent(ConfirmDeleteCardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
