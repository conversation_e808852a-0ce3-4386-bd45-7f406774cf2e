import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { NbButtonModule, NbIconModule, NbWindowRef } from '@nebular/theme';

@Component({
  selector: 'ni-confirm-delete-card',
  standalone: true,
  imports: [NbButtonModule, NbIconModule],
  templateUrl: './confirm-delete-card.component.html',
  styleUrl: './confirm-delete-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-popup text-center' },
})
export class ConfirmDeleteCardComponent {
  private windowRef = inject(NbWindowRef);

  doCancel() {
    this.windowRef.close({ cancel: true });
  }

  doDelete() {
    this.windowRef.close({ delete: true });
  }
}
