<ng-template #autoTopupCardTemplate>
  <p>
    Are you sure you want to remove this card? Removing this card will disable auto top-up use of this card. Please select another card to
    use for auto top-up.
  </p>
  <form [formGroup]="form" (ngSubmit)="doSubmitUpdateAutoTopupCard()" class="mb-4">
    <div class="row">
      <div class="col-12 col-sm-8 offset-sm-2">
        <nb-form-field>
          <nb-select
            [optionsOverlayOffset]="0"
            [status]="form.controls.auto_topup_card_id.dirty ? (!form.controls.auto_topup_card_id.value ? 'danger' : 'basic') : 'basic'"
            class="status-neural"
            [attr.aria-invalid]="form.controls.auto_topup_card_id.dirty && !form.controls.auto_topup_card_id.value ? true : null"
            placeholder="Select card"
            formControlName="auto_topup_card_id"
          >
            @for (item of otherCards(); track item.id) {
            <nb-option [value]="item.id" class="option-chevron ni-cards-option text-capitalize"
              >{{ item.brand }}&nbsp;****{{ item.last4 }}</nb-option
            >
            }
          </nb-select>
        </nb-form-field>
      </div>
    </div>
    <div class="ni-actions">
      <button class="ni-danger-btn" type="submit" [disabled]="processing()" nbButton status="danger" shape="round" size="medium">
        <nb-icon class="btn-icon h-auto" icon="error_outline" />Remove Card
      </button>
      <button (click)="doCancel()" nbButton status="basic" shape="round" size="medium">Cancel</button>
    </div>
  </form>
</ng-template>

<ng-template #noOtherCardsTemplate>
  <p>Are you sure you want to remove this card? Removing this card will disable auto top-up use of this card.</p>
  <div class="ni-actions">
    <button
      class="ni-danger-btn"
      (click)="doConfirmDeleteCard()"
      [disabled]="processing()"
      nbButton
      status="danger"
      shape="round"
      size="medium"
    >
      <nb-icon class="btn-icon h-auto" icon="error_outline"></nb-icon>Remove Card
    </button>
    <button (click)="doCancel()" nbButton status="basic" shape="round" size="medium">Cancel</button>
  </div>
</ng-template>

<ng-template #deleteCardTemplate>
  <p>Are you sure you want to delete Card?</p>
  <div class="ni-actions">
    <button class="ni-danger-btn" (click)="doDelete()" [disabled]="processing()" nbButton status="danger" shape="round" size="medium">
      <nb-icon class="btn-icon h-auto" icon="error_outline"></nb-icon>Remove Card
    </button>
    <button (click)="doCancel()" nbButton status="basic" shape="round" size="medium">Cancel</button>
  </div>
</ng-template>

@if(context.id){ @if(isAutoTopupActive() && isCardIdContextIdSame()){ @if(otherCards().length){
<ng-container *ngTemplateOutlet="autoTopupCardTemplate"></ng-container>
} @else{
<ng-container *ngTemplateOutlet="noOtherCardsTemplate"></ng-container>
} } @else{
<ng-container *ngTemplateOutlet="deleteCardTemplate"></ng-container>
} }
