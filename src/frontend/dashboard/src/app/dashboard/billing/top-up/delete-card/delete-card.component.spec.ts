import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  NbFocusMonitor,
  NbOverlayService,
  NbSelectModule,
  NbStatusService,
  NbThemeModule,
  NbWindowConfig,
  NbWindowRef,
  NbWindowService,
} from '@nebular/theme';
import { provideNoopSessionStore, provideNoopUserStore } from '@neural/auth/testing';
import { provideNoopUIStore } from '@neural/components/testing';
import { provideNoopCardsStore } from '@neural/data-access/testing';
import { provideNoopAuthService } from '@neural/query/testing';
import { MockComponent, MockProvider } from 'ng-mocks';
import { provideNgxMask } from 'ngx-mask';
import { of } from 'rxjs';
import { ConfirmDeleteCardComponent } from '../confirm-delete-card/confirm-delete-card.component';
import { DeleteCardComponent } from './delete-card.component';

describe('DeleteCardComponent', () => {
  let component: DeleteCardComponent;
  let fixture: ComponentFixture<DeleteCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DeleteCardComponent, NbSelectModule, MockComponent(ConfirmDeleteCardComponent), NbThemeModule.forRoot()],
      providers: [
        MockProvider(NbFocusMonitor, { monitor: () => of(null) }),
        MockProvider(NbWindowRef, {
          config: {
            context: 'id',
          } as unknown as NbWindowConfig,
        }),
        MockProvider(NbStatusService),
        provideNoopSessionStore(),
        provideNoopUserStore(),
        provideNoopUIStore(),
        provideNoopAuthService(),
        provideNoopCardsStore(),
        provideNgxMask(),
        MockProvider(NbOverlayService),
        MockProvider(NbWindowService),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DeleteCardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
