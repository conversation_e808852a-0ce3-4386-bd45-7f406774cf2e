import { JsonPipe, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, signal, Signal } from '@angular/core';
import { AbstractControl, FormBuilder, FormsModule, ReactiveFormsModule, ValidationErrors } from '@angular/forms';
import {
  NbButtonModule,
  NbFormFieldModule,
  NbIconModule,
  NbSelectModule,
  NbSpinnerModule,
  NbWindowRef,
  NbWindowService,
} from '@nebular/theme';
import { UserStore } from '@neural/auth';
import { CardsStore } from '@neural/data-access';
import { ICard } from '@neural/models';
import { tap } from 'ramda';
import { ConfirmDeleteCardComponent } from '../confirm-delete-card/confirm-delete-card.component';

@Component({
  selector: 'ni-delete-card',
  standalone: true,
  imports: [
    NbButtonModule,
    NbSelectModule,
    NbFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    NbSpinnerModule,
    NbIconModule,
    NgTemplateOutlet,
    ConfirmDeleteCardComponent,
    JsonPipe,
  ],
  templateUrl: './delete-card.component.html',
  styleUrl: './delete-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-popup text-center' },
})
export class DeleteCardComponent {
  protected windowService = inject(NbWindowService);
  cards = inject(CardsStore);
  user = inject(UserStore);
  otherCards: Signal<ICard[]> = computed(() => {
    const cards = this.cards.list();
    const context = this.windowRef.config.context! as { id: string };
    return cards.filter((card) => card.id !== context.id);
  });
  processing = signal(false);
  private windowRef = inject(NbWindowRef);
  context = this.windowRef.config.context! as { id: string };
  isAutoTopupActive: Signal<boolean> = computed(() => !!this.user.user()?.auto_topup_active);
  isCardIdContextIdSame: Signal<boolean> = computed(() => this.context.id === this.user.user()?.auto_topup_card_id);

  readonly form = inject(FormBuilder).nonNullable.group(
    {
      auto_topup_card_id: [null as string | null],
    },
    {
      validators: [
        (form: AbstractControl): ValidationErrors | null => {
          if (!form.value.auto_topup_card_id) {
            return { required: true };
          }
          return null;
        },
      ],
    },
  );

  doCancel() {
    this.windowRef.close();
  }

  doDelete() {
    // TODO validate incoming context.id
    const context = this.windowRef.config.context! as { id: string };

    this.cards.deleteCard(context.id).subscribe();
    this.windowRef.close();
  }

  doSubmitUpdateAutoTopupCard() {
    if (!this.form.valid) {
      this.form.get('auto_topup_card_id')!.markAsDirty();
      return;
    }

    const auto_topup_card_id = this.form.get('auto_topup_card_id')?.value;

    if (!auto_topup_card_id) {
      console.error('Auto top-up card ID is not available.');
      return;
    }
    this.processing.update(() => true);
    this.user
      .updateProfile({ auto_topup_card_id })
      .pipe(tap(() => this.form.markAsPristine()))
      .subscribe({
        next: () => this.doDelete(),
        error: (err) => {
          console.error('Error updating profile:', err);
        },
      });
  }

  doConfirmDeleteCard() {
    const dialogRef = this.windowService.open(ConfirmDeleteCardComponent, {
      title: 'Continue without auto top-up?',
      closeOnBackdropClick: true,
      windowClass: 'ni-window-small',
    });

    dialogRef.onClose.subscribe((data: { delete: boolean; cancel: boolean }) => {
      if (data.delete) {
        this.doDelete();
      }
      if (data.cancel) {
        this.windowRef.close();
      }
    });
  }
}
