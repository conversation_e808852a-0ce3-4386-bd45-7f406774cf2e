<ng-container>
  <div class="ni-list ni-list-table">
    <nb-list>
      <nb-list-item class="ni-list-header">
        <span class="col-comp-name">Company Name</span>
        <span class="col-comp-id">Company Id</span>
        <span class="col-tax-id">Tax ID</span>
        <span class="col-address">Address</span>
        <span class="col-country">Country</span>
        <span class="col-actions">&nbsp;</span>
      </nb-list-item>
      @for (item of cards.list(); track item.id) {
      <nb-list-item>
        <span class="col-comp-name">{{ item.company_name }}</span>
        <span class="col-comp-id">{{ item.company_id }}</span>
        <span class="col-tax-id">{{ item.tax_id }}</span>
        <span class="col-address">{{ item.address_line1 }}</span>
        <span class="col-country">{{ item.country | intlDisplayName }}</span>
        <span class="col-actions">
          <button class="btn-icon" [disabled]="payments.processing()" (click)="doDelete(item.id)" nbButton ghost>
            <nb-icon icon="delete" />
          </button>
        </span>
      </nb-list-item>
      } @empty {
      <nb-list-item>
        <td colspan="7" class="text-center py-2">No cards added yet</td>
      </nb-list-item>
      }
    </nb-list>
  </div>
</ng-container>
