.ni-list {
  // overflow: hidden;
}

nb-list-item {
  > span:first-child,
  > span:last-child {
    flex: 0 0 44px;
  }
}

nb-radio ::ng-deep label {
  padding-right: 16px !important;
}

nb-checkbox {
  display: block;
  margin-top: calc(var(--bs-gutter-y) * 2 / 3);
  padding-left: 8px;
}

.col-actions {
  width: 7%;
}
.col-comp-name,
.col-comp-id {
  min-width: 150px;
}

.col-tax-id,
.col-address,
.col-country {
  min-width: 100px;
}

.col-comp-name,
.col-tax-id,
.col-comp-id,
.col-address,
.col-country {
  width: 18%;
}
