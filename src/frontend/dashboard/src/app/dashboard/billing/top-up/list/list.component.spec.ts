import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, FormGroup } from '@angular/forms';
import { NbStatusService } from '@nebular/theme';
import { RadioGroupDirective } from '@neural/components';
import { provideNoopCardsStore, provideNoopPaymentsStore } from '@neural/data-access/testing';
import { MockDirective, MockProvider } from 'ng-mocks';
import { BillingTopUpListComponent } from './list.component';

describe('BillingTopUpListComponent', () => {
  let component: BillingTopUpListComponent;
  let fixture: ComponentFixture<BillingTopUpListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BillingTopUpListComponent, MockDirective(RadioGroupDirective)],
      providers: [MockProvider(NbStatusService), provideNoopCardsStore(), provideNoopPaymentsStore()],
    }).compileComponents();

    fixture = TestBed.createComponent(BillingTopUpListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
