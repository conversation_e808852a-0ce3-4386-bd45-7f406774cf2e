import { Component, inject, input, output } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { NbButtonModule, NbCheckboxModule, NbIconModule, NbListModule, NbRadioModule } from '@nebular/theme';
import { IntlDisplayNamePipe } from '@neural/components';
import { CardsStore, PaymentsStore } from '@neural/data-access';

@Component({
  selector: 'ni-billing-top-up-list',
  standalone: true,
  imports: [ReactiveFormsModule, NbButtonModule, NbCheckboxModule, NbIconModule, NbRadioModule, NbListModule, IntlDisplayNamePipe],
  templateUrl: './list.component.html',
  styleUrl: './list.component.scss',
})
export class BillingTopUpListComponent {
  cards = inject(CardsStore);
  payments = inject(PaymentsStore);

  deleteCard = output<string>();

  doDelete(id: string) {
    this.deleteCard.emit(id);
  }
}
