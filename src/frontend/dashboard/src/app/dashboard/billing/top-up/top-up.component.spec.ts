import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbFocusMonitor, NbStatusService, NbWindowService } from '@nebular/theme';
import { provideNoopUserStore } from '@neural/auth/testing';
import { IconsModule } from '@neural/components';
import { provideNoopUIStore } from '@neural/components/testing';
import { provideNoopCardsStore, provideNoopPaymentsStore } from '@neural/data-access/testing';
import { MockComponent, MockProvider } from 'ng-mocks';
import { provideNgxMask } from 'ngx-mask';
import { provideNgxStripe } from 'ngx-stripe';
import { of } from 'rxjs';
import { BillingTopUpListComponent } from './list/list.component';
import { TopUpComponent } from './top-up.component';

describe('TopUpComponent', () => {
  let component: TopUpComponent;
  let fixture: ComponentFixture<TopUpComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TopUpComponent, IconsModule, MockComponent(BillingTopUpListComponent)],
      providers: [
        MockProvider(NbFocusMonitor, { monitor: () => of(null) }),
        MockProvider(NbStatusService),
        MockProvider(NbWindowService),
        provideNgxStripe(process.env.STRIPE_API_KEY, {
          locale: 'en',
        }),
        provideNoopUserStore(),
        provideNoopCardsStore(),
        provideNoopPaymentsStore(),
        provideNoopUIStore(),
        provideNgxMask(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TopUpComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
