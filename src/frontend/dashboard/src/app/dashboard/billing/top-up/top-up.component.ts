import { CurrencyPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, HostBinding, inject } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NbButtonModule, NbFormFieldModule, NbIconModule, NbInputModule, NbWindowService } from '@nebular/theme';
import { UserStore } from '@neural/auth';
import { CentsToDollarsPipe, UIStore } from '@neural/components';
import { CardsStore, PaymentsStore } from '@neural/data-access';
import { MIN_TOPUP_AMOUNT_IN_CENTS } from '@neural/models';
import { NgxMaskDirective } from 'ngx-mask';
import { injectStripe } from 'ngx-stripe';
import { TopUpModalComponent } from '../../gpu/top-up-modal/top-up-modal.component';
import { AddCardComponent } from './add-card/add-card.component';
import { DeleteCardComponent } from './delete-card/delete-card.component';
import { BillingTopUpListComponent } from './list/list.component';

@Component({
  selector: 'ni-dashboard-billing-top-up',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NbButtonModule,
    NbFormFieldModule,
    NbIconModule,
    NbInputModule,
    NgxMaskDirective,
    BillingTopUpListComponent,
    CentsToDollarsPipe,
  ],
  providers: [CurrencyPipe],
  templateUrl: './top-up.component.html',
  styleUrl: './top-up.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-card' },
})
export class TopUpComponent {
  protected windowService = inject(NbWindowService);
  protected currencyPipe = inject(CurrencyPipe);

  cards = inject(CardsStore);
  payments = inject(PaymentsStore);
  ui = inject(UIStore);
  stripe = injectStripe();
  user = inject(UserStore);

  readonly MIN_TOPUP_AMOUNT_IN_CENTS = MIN_TOPUP_AMOUNT_IN_CENTS;
  readonly MIN_TOPUP_AMOUNT_IN_DOLLARS = MIN_TOPUP_AMOUNT_IN_CENTS / 100;

  @HostBinding('class.ni-has-cards')
  get hasCards() {
    return Boolean(this.cards.list().length);
  }

  addCard() {
    this.windowService.open(AddCardComponent, { title: 'Billing Details', windowClass: 'ni-window-full-screen' });
  }

  onTopUpBalance() {
    this.windowService.open(TopUpModalComponent, {
      title: 'Top Up',
      closeOnBackdropClick: true,
      windowClass: 'ni-window-small',
    });
  }

  deleteCard(id: string) {
    this.windowService.open(DeleteCardComponent, {
      title: 'Remove Card',
      closeOnBackdropClick: true,
      context: { id },
      windowClass: 'ni-window-small',
    });

    // Remove the class after opening the dialog
    setTimeout(() => {
      document.documentElement.classList.remove('cdk-global-scrollblock');
    }, 0);
  }
}
