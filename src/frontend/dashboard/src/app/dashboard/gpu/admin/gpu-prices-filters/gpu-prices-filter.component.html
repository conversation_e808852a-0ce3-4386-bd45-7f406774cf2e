<div class="ni-popover mx-4 mt-2 mb-0">
  <h6 class="mt-0 ms-3">Specs</h6>

  <div class="ni-filter-grid" [formGroup]="form">
    <!-- Price range-->
    <div class="g-col-sm-6 g-col-12 mx-3">
      <div class="d-flex justify-content-between">
        <span class="filter-name">PRICE RANGE</span>
        <span class="filter-min-max-description">
          <span> {{ form.value.price_range_min }} </span> - <span> {{ form.value.price_range_max }} </span> $/hr</span
        >
      </div>

      <mat-slider [min]="PRICE_MIN" [max]="PRICE_MAX" step="0.1" tickInterval="0.5">
        <input formControlName="price_range_min" matSliderStartThumb />
        <input formControlName="price_range_max" matSliderEndThumb />
      </mat-slider>

      <div class="d-flex justify-content-between">
        <span class="filter-min-max-value">{{ PRICE_MIN }}</span>
        <span class="filter-min-max-value">{{ PRICE_MAX }}</span>
      </div>
    </div>

    <!-- VRAM-->
    <div class="g-col-sm-6 g-col-12 mx-3">
      <div class="d-flex justify-content-between">
        <span class="filter-name">VRAM</span>
        <span class="filter-min-max-description">{{ form.value.gpu_capacity_min }} - {{ form.value.gpu_capacity_max }} GB</span>
      </div>
      <mat-slider [min]="VRAM_MIN" [max]="VRAM_MAX">
        <input formControlName="gpu_capacity_min" matSliderStartThumb />
        <input formControlName="gpu_capacity_max" matSliderEndThumb />
      </mat-slider>
      <div class="d-flex justify-content-between">
        <span class="filter-min-max-value">{{ VRAM_MIN }}</span>
        <span class="filter-min-max-value">{{ VRAM_MAX }}</span>
      </div>
    </div>

    <div class="g-col-12 grid ni-actions ni-actions-responsive ni-actions-responsive-ext mb-0">
      <button
        class="g-col-sm-6 g-col-12 action-button"
        (click)="filterClose()"
        type="button"
        nbButton
        status="basic"
        shape="round"
        size="medium"
      >
        Discard
      </button>
      <button
        class="g-col-sm-6 g-col-12 action-button"
        (click)="filterSearch()"
        type="button"
        nbButton
        status="primary"
        shape="round"
        size="medium"
      >
        Search with filter
      </button>
    </div>
  </div>
</div>
