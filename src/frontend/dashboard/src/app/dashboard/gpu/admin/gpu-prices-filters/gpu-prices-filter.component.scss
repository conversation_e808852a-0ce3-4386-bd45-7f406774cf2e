@use '@nebular/theme/styles/theming' as *;
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';

:host {
  h6 {
    margin-bottom: 16px;
  }

  mat-slider {
    width: 100%;
    margin: 0;
  }
  .ni-popover {
    width: 560px;
    .ni-actions-responsive-ext {
      column-gap: 48px;
    }
  }
  @include media-breakpoint-down(md) {
    .ni-popover {
      width: auto;
    }
  }
}

.filter-min-max-value {
  font-size: 10px;
  font-weight: 700;
  line-height: 12.1px;
  letter-spacing: 0.03em;
  text-align: left;
  color: nb-theme(text-highlight-color);
}

.filter-name {
  font-size: 10px;
  font-weight: 700;
  line-height: 12.1px;
  letter-spacing: 0.03em;
}
.filter-min-max-description {
  font-size: 14px;
  font-weight: 400;
  line-height: 16.8px;
  min-width: 80px; /* Adjust as needed */
}
.filter-name,
.filter-min-max-description {
  color: nb-theme(layout-text-color);
}

.action-button {
  width: 200px;
  @include media-breakpoint-down(sm) {
    @extend .w-100 !optional;
  }
}
