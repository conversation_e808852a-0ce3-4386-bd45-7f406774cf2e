import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NbStatusService } from '@nebular/theme';
import { MockProvider } from 'ng-mocks';
import { GpuPricesFilterComponent } from './gpu-prices-filter.component';

describe('GpuPricesFilterComponent', () => {
  let component: GpuPricesFilterComponent;
  let fixture: ComponentFixture<GpuPricesFilterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GpuPricesFilterComponent],
      providers: [MockProvider(NbStatusService)],
    }).compileComponents();

    fixture = TestBed.createComponent(GpuPricesFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
