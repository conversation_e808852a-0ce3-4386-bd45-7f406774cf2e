import { ChangeDetectionStrategy, Component, OnInit, inject, input, output } from '@angular/core';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatSliderModule } from '@angular/material/slider';
import { NbButtonModule } from '@nebular/theme';
import { IGpuPricesFilterSearch } from '@neural/models';

@Component({
  selector: 'ni-gpu-filter-popover',
  standalone: true,
  imports: [MatSliderModule, NbButtonModule, ReactiveFormsModule],
  templateUrl: './gpu-prices-filter.component.html',
  styleUrl: './gpu-prices-filter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { class: 'ni-popover' },
})
export class GpuPricesFilterComponent implements OnInit {
  submitEvent = output<IGpuPricesFilterSearch>();
  closeEvent = output<boolean>();
  formData = input<IGpuPricesFilterSearch>();

  readonly PRICE_MIN = 0;
  readonly PRICE_MAX = 10;

  readonly VRAM_MIN = 0;
  readonly VRAM_MAX = 640;

  readonly form = inject(FormBuilder).group({
    price_range_min: [this.PRICE_MIN],
    price_range_max: [this.PRICE_MAX],
    gpu_capacity_min: [this.VRAM_MIN],
    gpu_capacity_max: [this.VRAM_MAX],
  });

  filterClose() {
    this.form.reset();
    this.closeEvent.emit(true);
  }

  ngOnInit() {
    this.form.patchValue({
      price_range_min: this.formData()?.price_range_min ?? this.PRICE_MIN,
      price_range_max: this.formData()?.price_range_max ?? this.PRICE_MAX,
      gpu_capacity_min: this.formData()?.gpu_capacity_min ?? this.VRAM_MIN,
      gpu_capacity_max: this.formData()?.gpu_capacity_max ?? this.VRAM_MAX,
    });
  }

  filterSearch() {
    const formData: IGpuPricesFilterSearch = {
      price_range_min: this.form.get('price_range_min')?.value ?? undefined,
      price_range_max: this.form.get('price_range_max')?.value ?? undefined,
      gpu_capacity_min: this.form.get('gpu_capacity_min')?.value ?? undefined,
      gpu_capacity_max: this.form.get('gpu_capacity_max')?.value ?? undefined,
    };

    this.submitEvent.emit(formData);
  }
}
