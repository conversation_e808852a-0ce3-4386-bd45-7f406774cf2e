import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, HostListener, inject, OnInit, signal, ViewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  NbButtonModule,
  NbCardModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbListModule,
  NbPopoverDirective,
  NbPopoverModule,
  NbSpinnerModule,
  NbWindowService,
} from '@nebular/theme';
import { GpuPricesStore } from '@neural/data-access';
import { IGpuPricesFilterSearch } from '@neural/models';
import { debounceTime } from 'rxjs';
import { GpuPricesFilterComponent } from '../gpu-prices-filters/gpu-prices-filter.component';
import { GpuPricesListComponent } from '../gpu-prices-list/gpu-prices-list.component';

@Component({
  selector: 'ni-gpu-prices',
  standalone: true,
  imports: [
    GpuPricesListComponent,
    NbButtonModule,
    NbInputModule,
    NbCardModule,
    NbIconModule,
    NbFormFieldModule,
    NbPopoverModule,
    GpuPricesFilterComponent,
    ReactiveFormsModule,
    NbSpinnerModule,
    NbListModule,
    CommonModule,
  ],
  templateUrl: './gpu-prices.component.html',
  styleUrl: './gpu-prices.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GpuPricesComponent implements OnInit {
  protected windowService = inject(NbWindowService);

  gpuPricesStore = inject(GpuPricesStore);
  filtersApplied = signal(false);

  @ViewChild(NbPopoverDirective) gpuFilterPopover!: NbPopoverDirective;

  @HostListener('click')
  clicked() {
    if (this.gpuFilterPopover.isShown) {
      this.gpuFilterPopover.hide();
    }
  }

  gpu_name = new FormControl<string>('');

  ngOnInit() {
    this.gpu_name.valueChanges.pipe(debounceTime(500)).subscribe((value) => {
      this.filtersApplied.set(true);
      this.gpuPricesStore.filter({ ...this.gpuPricesStore.params(), gpu_name: value ?? undefined });
    });

    this.gpuPricesStore.filter({});
  }

  filterClose() {
    this.gpu_name.reset('', { emitEvent: false });
    this.gpuPricesStore.filter({});
    this.gpuFilterPopover.hide();
    this.filtersApplied.set(false);
  }

  filterOpen(event: MouseEvent) {
    event.stopPropagation();
    if (this.gpuFilterPopover.isShown) {
      this.gpuFilterPopover.hide();
    } else {
      this.gpuFilterPopover.show();
    }
  }

  filterSearch(submitData: IGpuPricesFilterSearch) {
    this.gpuPricesStore.filter({ ...this.gpuPricesStore.params(), ...submitData });
    this.filtersApplied.set(true);
    this.gpuFilterPopover.hide();
  }
}
